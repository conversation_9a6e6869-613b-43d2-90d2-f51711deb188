---
description: Learn about the PosePredictor class for YOLO model predictions on pose data. Get setup instructions, example usage, and implementation details.
keywords: YOLO, Pose Prediction, Ultralytics, PosePredictor, YOLOv8, Machine Learning, Deep Learning, Python, AI Models
---

# Reference for `ultralytics/models/yolo/pose/predict.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/predict.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/predict.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/pose/predict.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.pose.predict.PosePredictor

<br><br>
