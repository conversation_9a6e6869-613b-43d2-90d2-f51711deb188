---
description: Explore detailed functionalities of Ultralytics plotting utilities for data visualizations and custom annotations in ML projects.
keywords: ultralytics, plotting, utilities, documentation, data visualization, annotations, python, ML tools
---

# Reference for `ultralytics/utils/plotting.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/plotting.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/plotting.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/plotting.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.plotting.Colors

<br><br>

## ::: ultralytics.utils.plotting.Annotator

<br><br>

## ::: ultralytics.utils.plotting.plot_labels

<br><br>

## ::: ultralytics.utils.plotting.save_one_box

<br><br>

## ::: ultralytics.utils.plotting.plot_images

<br><br>

## ::: ultralytics.utils.plotting.plot_results

<br><br>

## ::: ultralytics.utils.plotting.plt_color_scatter

<br><br>

## ::: ultralytics.utils.plotting.plot_tune_results

<br><br>

## ::: ultralytics.utils.plotting.output_to_target

<br><br>

## ::: ultralytics.utils.plotting.output_to_rotated_target

<br><br>

## ::: ultralytics.utils.plotting.feature_visualization

<br><br>
