# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]
fusion_mode: bifpn
node_mode: CSP_MSCB
head_channel: 256

backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, SCDown, [512, 3, 2]] # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, SCD<PERSON>, [1024, 3, 2]] # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SPPF, [1024, 5]] # 9
  - [-1, 1, PSA, [1024]] # 10

# YOLOv10.0n head
head:
  - [4, 1, Conv, [head_channel]]  # 11-P3/8
  - [6, 1, Conv, [head_channel]]  # 12-P4/16
  - [10, 1, Conv, [head_channel]]  # 13-P5/32

  - [12, 1, Conv, [head_channel, 3, 2]] # 14-P5/32
  - [[-1, 13], 1, MFM, [head_channel]] # 15
  - [-1, 3, node_mode, [head_channel, [5,7,9]]] # 16-P5/32

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] # 17-P4/16
  - [11, 1, Conv, [head_channel, 3, 2]] # 18-P4/16
  - [[-1, -2, 12], 1, MFM, [head_channel]] # 19
  - [-1, 3, node_mode, [head_channel, [3,5,7]]] # 20-P4/16

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] # 21-P3/8
  - [2, 1, Conv, [head_channel, 3, 2]] # 22-P3/8
  - [[-1, -2, 11], 1, MFM, [head_channel]] # 23
  - [-1, 3, node_mode, [head_channel, [1,3,5]]] # 24-P3/8

  - [[21, -1], 1, MFM, [head_channel]] # 25
  - [-1, 3, node_mode, [head_channel, [1,3,5]]] # 26-P3/8

  - [24, 1, Conv, [head_channel, 3, 2]] # 27-P4/16
  - [26, 1, Conv, [head_channel, 3, 2]] # 28-P4/16
  - [[-1, -2, 20, 17], 1, MFM, [head_channel]] # 29-P4/16
  - [-1, 3, node_mode, [head_channel, [3,5,7]]] # 30-P4/16

  - [20, 1, Conv, [head_channel, 3, 2]] # 31-P5/32
  - [30, 1, Conv, [head_channel, 3, 2]] # 32-P5/32
  - [[-1, -2, 16], 1, MFM, [head_channel]] # 33-P5/32
  - [-1, 3, node_mode, [head_channel, [5,7,9]]] # 34-P5/32

  - [[26, 30, 34], 1, v10Detect, [nc]] # Detect(P3, P4, P5)