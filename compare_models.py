import warnings
warnings.filterwarnings('ignore')
import torch
from ultralytics import <PERSON>OL<PERSON>
from ultralytics.utils.torch_utils import model_info

def compare_models():
    print("正在比较 YOLOv8-GFPN 和标准 YOLOv8n 的参数量...")
    print("=" * 80)
    
    # 标准YOLOv8n模型
    print("标准 YOLOv8n:")
    try:
        model_standard = YOLO('ultralytics/cfg/models/v8/yolov8.yaml')
        model_standard.fuse()
        n_l_std, n_p_std, n_g_std, flops_std = model_info(model_standard.model)
        print(f"  层数: {n_l_std}")
        print(f"  参数量: {n_p_std:,}")
        print(f"  梯度数: {n_g_std:,}")
        print(f"  计算量: {flops_std:.2f} GFLOPs")
    except Exception as e:
        print(f"  错误: {e}")
        return
    
    print("\n" + "-" * 40 + "\n")
    
    # YOLOv8-GFPN模型
    print("YOLOv8-GFPN:")
    try:
        model_gfpn = YOLO('ultralytics/cfg/models/v8/yolov8-GFPN.yaml')
        model_gfpn.fuse()
        n_l_gfpn, n_p_gfpn, n_g_gfpn, flops_gfpn = model_info(model_gfpn.model)
        print(f"  层数: {n_l_gfpn}")
        print(f"  参数量: {n_p_gfpn:,}")
        print(f"  梯度数: {n_g_gfpn:,}")
        print(f"  计算量: {flops_gfpn:.2f} GFLOPs")
    except Exception as e:
        print(f"  错误: {e}")
        return
    
    print("\n" + "=" * 80)
    print("对比结果:")
    print("=" * 80)
    
    # 计算差异
    param_diff = n_p_gfpn - n_p_std
    param_ratio = (n_p_gfpn / n_p_std) * 100
    flops_diff = flops_gfpn - flops_std
    flops_ratio = (flops_gfpn / flops_std) * 100
    layer_diff = n_l_gfpn - n_l_std
    
    print(f"参数量对比:")
    print(f"  YOLOv8-GFPN vs 标准YOLOv8n: {n_p_gfpn:,} vs {n_p_std:,}")
    print(f"  参数增加: {param_diff:,} ({param_diff/n_p_std*100:.2f}%)")
    print(f"  参数比例: {param_ratio:.2f}%")
    
    print(f"\n计算量对比:")
    print(f"  YOLOv8-GFPN vs 标准YOLOv8n: {flops_gfpn:.2f} vs {flops_std:.2f} GFLOPs")
    print(f"  计算量增加: {flops_diff:.2f} GFLOPs ({flops_diff/flops_std*100:.2f}%)")
    print(f"  计算量比例: {flops_ratio:.2f}%")
    
    print(f"\n层数对比:")
    print(f"  YOLOv8-GFPN vs 标准YOLOv8n: {n_l_gfpn} vs {n_l_std}")
    print(f"  层数增加: {layer_diff}")

if __name__ == '__main__':
    compare_models() 