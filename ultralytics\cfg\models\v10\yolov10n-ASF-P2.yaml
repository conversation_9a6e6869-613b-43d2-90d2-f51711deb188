# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, SCDown, [512, 3, 2]] # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, SCDown, [1024, 3, 2]] # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SPP<PERSON>, [1024, 5]] # 9
  - [-1, 1, PSA, [1024]] # 10

# YOLOv10.0n head
head:
  - [-1, 1, Conv, [512, 1, 1]] # 11
  - [4, 1, Conv, [512, 1, 1]] # 12
  - [[-1, 6, -2], 1, Zoom_cat, []]  # 12 cat backbone P4
  - [-1, 3, C2f, [512]]  # 14

  - [-1, 1, Conv, [256, 1, 1]] # 15
  - [2, 1, Conv, [256, 1, 1]] # 16
  - [[-1, 4, -2], 1, Zoom_cat, []]  # 17  cat backbone P3
  - [-1, 3, C2f, [256]]  # 18 (P3/8-small)

  - [-1, 1, Conv, [256, 3, 2]] # 19
  - [[-1, 15], 1, Concat, [1]]  # 20 cat head P4
  - [-1, 3, C2f, [512]]  # 21 (P4/16-medium)

  - [-1, 1, SCDown, [512, 3, 2]] # 22
  - [[-1, 11], 1, Concat, [1]]  # 23 cat head P5
  - [-1, 3, C2fCIB, [1024, True, True]]  # 24 (P5/32-large)

  - [[4, 6, 8], 1, ScalSeq, [256]] # 25 args[inchane]
  - [[18, -1], 1, Add, []] # 26
  # - [[18, -1], 1, asf_attention_model, []] # 26 可以自行替换，上面的是普通的add，这个是asf文章中的注意力机制

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 27
  - [[-1, 2], 1, Concat, []]  # 28  cat backbone P2
  - [-1, 3, C2f, [128]]  # 29 (P2/4-small)

  - [[2, 26, 21], 1, ScalSeq, [128]] # 30 args[inchane]
  - [[29, -1], 1, Add, []] # 31
  # - [[29, -1], 1, asf_attention_model, []] # 31

  - [[31, 26, 21, 24], 1, v10Detect, [nc]]  # Detect(P2, P3, P4, P5)