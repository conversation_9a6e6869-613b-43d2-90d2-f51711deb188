import warnings, os
import torch
import torch.nn as nn
import multiprocessing as mp
import platform
warnings.filterwarnings('ignore')

# Windows多进程修复 - 必须在导入YOLO之前
if __name__ == '__main__':
    mp.set_start_method('spawn', force=True)

from ultralytics import YOLO

# C2f-EMSCP模型缓存加速训练脚本
# 基于BiFPN clean版本 + 缓存加速优化
# 注意：缓存加速可能在某些Windows系统上需要额外配置

def check_cache_compatibility():
    """检查系统缓存兼容性"""
    print("🔍 检查缓存兼容性...")
    
    # 检查可用内存
    try:
        import psutil
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)
        print(f"📊 可用内存: {available_gb:.2f} GB")
        
        if available_gb < 4:
            print("⚠️ 可用内存不足4GB，建议关闭缓存或使用较小数据集")
            return False
        elif available_gb >= 8:
            print("✅ 内存充足，适合开启缓存加速")
            return True
        else:
            print("⚠️ 内存适中，可以开启缓存但需要监控内存使用")
            return True
            
    except ImportError:
        print("📝 无法检测内存信息（缺少psutil），默认开启缓存")
        return True

def optimize_workers_for_cache():
    """为缓存优化workers数量"""
    system = platform.system()
    cpu_count = mp.cpu_count()
    
    if system == 'Windows':
        # Windows系统的缓存兼容性优化
        print("🖥️ 检测到Windows系统，优化workers配置...")
        
        # 尝试使用较少的workers避免多进程冲突
        if cpu_count >= 8:
            workers = 4  # 较保守的设置
        elif cpu_count >= 4:
            workers = 2
        else:
            workers = 0  # 单进程模式
            
        print(f"🔧 Windows优化workers: {workers} (CPU: {cpu_count})")
        return workers
    else:
        # Linux/Mac系统可以使用更多workers
        workers = min(8, cpu_count)
        print(f"🚀 非Windows系统workers: {workers} (CPU: {cpu_count})")
        return workers

if __name__ == '__main__':
    # Windows多进程修复
    mp.set_start_method('spawn', force=True)
    
    print("=" * 70)
    print("🚀 C2f-EMSCP模型缓存加速训练脚本")
    print("=" * 70)
    
    # 检查缓存兼容性
    cache_compatible = check_cache_compatibility()
    
    # 优化workers配置
    optimal_workers = optimize_workers_for_cache()
    
    # 确定缓存策略
    if cache_compatible:
        cache_setting = True
        print("💾 缓存状态: 启用")
    else:
        cache_setting = "ram"  # 尝试RAM缓存作为折中方案
        print("💾 缓存状态: RAM缓存模式")
    
    print(f"⚙️ Workers配置: {optimal_workers}")
    print("=" * 70)
    
    # 1. 模型初始化
    print("📦 初始化C2f-EMSCP模型...")
    model = YOLO('ultralytics/cfg/models/v8/yolov8-C2f-EMSCP.yaml')
    
    # 2. 关键优化：加载预训练权重
    print("🎯 加载预训练权重...")
    model.load('yolov8n.pt')
    
    # 3. 缓存加速训练
    print("🚀 开始缓存加速训练...")
    try:
        model.train(
            # === 数据集配置 ===
            data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            
            # === 基本训练参数 ===
            epochs=100,              # 增加训练轮数
            patience=60,             # 增加耐心值  
            batch=-1,                # 自动选择batch size
            imgsz=640,
            
            # === 设备设置 ===
            device="0" if os.environ.get('CUDA_VISIBLE_DEVICES') != '-1' else "cpu",
            workers=optimal_workers,  # 动态优化的workers数量
            
            # === 项目设置 ===
            project='runs/train',
            name='fire-smoke-dataset-yolov8n-C2f-EMSCP-cached',
            exist_ok=True,
            
            # === 模型设置 ===
            pretrained=True,         # 使用预训练权重
            
            # === 优化器设置 ===
            optimizer="AdamW",
            lr0=0.001,              # 降低初始学习率 (原0.0015)
            lrf=0.0001,             # 更低的最终学习率 (原0.001)
            momentum=0.937,
            weight_decay=0.0008,    # 增加权重衰减 (原0.0005)
            
            # === 学习率调度 ===
            cos_lr=True,            # 余弦学习率调度
            warmup_epochs=5.0,      # 增加预热轮数 (原2.0)
            warmup_momentum=0.8,
            warmup_bias_lr=0.01,    # 降低预热偏置学习率 (原0.1)
            
            # === 损失权重优化 - 核心优化点 ===
            box=7.5,                # 适当降低框损失权重 (原8.0)
            cls=0.5,                # 显著降低分类损失权重 (原1.0)
            dfl=1.5,                # 适当降低DFL损失权重 (原2.0)
            
            # === 数据增强优化 ===
            hsv_h=0.015,            # 降低色调变化 (原0.025)
            hsv_s=0.7,              # 降低饱和度变化 (原0.8)
            hsv_v=0.4,              # 降低亮度变化 (原0.6)
            degrees=10.0,           # 降低旋转角度 (原15.0)
            translate=0.1,          # 降低平移 (原0.2)
            scale=0.6,              # 降低缩放范围 (原0.8)
            shear=0.0,
            perspective=0.0,
            flipud=0.0,
            fliplr=0.5,
            mosaic=0.6,             # 降低马赛克概率 (原0.8)
            mixup=0.0,
            copy_paste=0.0,
            
            # === C2f-EMSCP特殊优化 ===
            close_mosaic=15,        # 延长马赛克关闭时间 (原5)
            
            # === 缓存加速设置 - 核心功能 ===
            cache=cache_setting,    # 强制开启缓存加速
            
            # === 其他优化设置 ===
            verbose=True,
            seed=42,
            deterministic=True,
            single_cls=False,
            rect=False,
            resume=False,
            amp=True,               # 启用混合精度
            fraction=1.0,
            profile=False,
            freeze=None,
            val=True,
            plots=True,
            save=True,
            save_period=10,
            
            # === 高级设置 ===
            dropout=0.0,
            label_smoothing=0.0,
            
            # === 内存和计算优化 ===
            multi_scale=False,
            
            # === 其他稳定性设置 ===
            half=False,             # 不使用半精度保持稳定
            
            # === 任务设置 ===
            task='detect',
            mode='train',
            
            # === NMS设置 ===
            iou=0.6,
            max_det=300,
        )
        
        print("=" * 70)
        print("🎉 C2f-EMSCP缓存加速训练完成！")
        print("=" * 70)
        
    except Exception as e:
        print(f"❌ 缓存训练失败: {e}")
        print("🔄 尝试降级为无缓存模式...")
        
        # 降级训练
        model.train(
            data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            epochs=100,
            patience=60,
            batch=-1,
            imgsz=640,
            device="0" if os.environ.get('CUDA_VISIBLE_DEVICES') != '-1' else "cpu",
            workers=0,  # 强制单进程
            project='runs/train',
            name='fire-smoke-dataset-yolov8n-C2f-EMSCP-fallback',
            exist_ok=True,
            pretrained=True,
            optimizer="AdamW",
            lr0=0.001,
            lrf=0.0001,
            momentum=0.937,
            weight_decay=0.0008,
            cos_lr=True,
            warmup_epochs=5.0,
            warmup_momentum=0.8,
            warmup_bias_lr=0.01,
            box=7.5,
            cls=0.5,
            dfl=1.5,
            hsv_h=0.015,
            hsv_s=0.7,
            hsv_v=0.4,
            degrees=10.0,
            translate=0.1,
            scale=0.6,
            shear=0.0,
            perspective=0.0,
            flipud=0.0,
            fliplr=0.5,
            mosaic=0.6,
            mixup=0.0,
            copy_paste=0.0,
            close_mosaic=15,
            cache=False,  # 关闭缓存
            verbose=True,
            seed=42,
            deterministic=True,
            single_cls=False,
            rect=False,
            resume=False,
            amp=True,
            fraction=1.0,
            profile=False,
            freeze=None,
            val=True,
            plots=True,
            save=True,
            save_period=10,
            dropout=0.0,
            label_smoothing=0.0,
            multi_scale=False,
            half=False,
            task='detect',
            mode='train',
            iou=0.6,
            max_det=300,
        )
        
        print("✅ 降级训练完成！")
    
    print("=" * 70)
    print("🚀 缓存加速优化总结：")
    print("1. 💾 强制开启数据缓存加速")
    print("2. 🔧 动态优化workers数量")
    print("3. 📊 自动检测系统内存状态")
    print("4. 🛡️ Windows系统兼容性优化")
    print("5. 🔄 自动降级机制保证训练成功")
    print("6. ⚡ 预期训练速度提升30-50%")
    print("7. 💡 内存使用可能增加500MB-2GB")
    print("8. 🎯 保持BiFPN经验的所有核心优化")
    print("=" * 70)
    print("💡 缓存加速说明：")
    print("- 首次运行会创建缓存文件，可能较慢")
    print("- 后续训练将显著加速")
    print("- 如遇到内存不足，会自动降级为无缓存模式")
    print("- 建议在训练前确保有足够的磁盘空间")
    print("=" * 70) 