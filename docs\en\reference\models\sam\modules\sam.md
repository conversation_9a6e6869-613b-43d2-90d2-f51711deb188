---
description: Discover the Ultralytics Sam module for object segmentation. Learn about its components, such as image encoders and mask decoders, in this comprehensive guide.
keywords: Ultralytics, Sam <PERSON>, object segmentation, image encoder, mask decoder, prompt encoder, AI, machine learning
---

# Reference for `ultralytics/models/sam/modules/sam.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/sam.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/sam.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/modules/sam.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.sam.modules.sam.Sam

<br><br>
