import warnings, os
# os.environ["CUDA_VISIBLE_DEVICES"]="-1"    # 代表用cpu训练 不推荐！没意义！ 而且有些模块不能在cpu上跑
# os.environ["CUDA_VISIBLE_DEVICES"]="0"     # 代表用第一张卡进行训练  0：第一张卡 1：第二张卡
# 多卡训练参考<YOLOV8V10配置文件.md>下方常见错误和解决方案
warnings.filterwarnings('ignore')
from ultralytics import YOLO

# BILIBILI UP 魔傀面具
# 训练参数官方详解链接：https://docs.ultralytics.com/modes/train/#resuming-interrupted-trainings:~:text=a%20training%20run.-,Train%20Settings,-The%20training%20settings

# 指定显卡和多卡训练问题 统一都在<YOLOV8V10配置文件.md>下方常见错误和解决方案。
# 训练过程中loss出现nan，可以尝试关闭AMP，就是把下方amp=False的注释去掉。
# 训练时候输出的AMP Check使用的YOLOv8n的权重不是代表载入了预训练权重的意思，只是用于测试AMP，正常的不需要理会。
# 整合多个创新点的B站视频链接:https://www.bilibili.com/video/BV15H4y1Y7a2/
# 更多问题解答请看使用说明.md下方<常见疑问>

# YOLOV8源码常见疑问解答小课堂
# 1. [关于配置文件中Optimizer参数为auto的时候，究竟Optimizer会怎么选用呢？](https://www.bilibili.com/video/BV1K34y1w7cZ/)
# 2. [best.pt究竟是根据什么指标来保存的?](https://www.bilibili.com/video/BV1jN411M7MA/)
# 3. [数据增强在yolov8中的应用](https://www.bilibili.com/video/BV1aQ4y1g7ah/)
# 4. [如何添加FPS计算代码和FPS的相关的一些疑问](https://www.bilibili.com/video/BV1Sw411g7DD/)
# 5. [预测框粗细颜色修改与精度小数位修改](https://www.bilibili.com/video/BV12K421a7rH/)
# 6. [导出改进/剪枝的onnx模型和讲解onnx-opset和onnxsim的作用](https://www.bilibili.com/video/BV1CK421e7Y3/)
# 7. [YOLOV8模型详细讲解(包含该如何改进YOLOV8)(刚入门小白，需要改进YOLOV8的同学必看！)](https://www.bilibili.com/video/BV1Ms421u7VH/)
# 8. [学习率变化问题](https://www.bilibili.com/video/BV1frnferEL1/)

# 一些非常推荐小白看的视频链接
# 1. [YOLOV8模型详细讲解(包含该如何改进YOLOV8)(刚入门小白，需要改进YOLOV8的同学必看！)](https://www.bilibili.com/video/BV1Ms421u7VH/)
# 2. [提升多少才能发paper？轻量化需要看什么指标？需要轻量化到什么程度才能发paper？这期给大家一一解答！](https://www.bilibili.com/video/BV1QZ421M7gu/)
# 3. [深度学习实验部分常见疑问解答！(小白刚入门必看！少走弯路！少自我内耗！)](https://www.bilibili.com/video/BV1Bz421B7pC/)
#     ```
#     1. 如何衡量自己的所做的工作量够不够？
#     2. 为什么别人的论文说这个模块对xxx有作用，但是我自己用的时候还掉点了？
#     3. 提升是和什么模型相比呢 比如和yolov8这种基础模型比还是和别人提出的目前最好的模型比
#     4. 对比不同的模型的时候，输入尺寸，学习率，学习次数这些是否需要一致？
#     ```
# 4. [深度学习实验部分常见疑问解答二！(小白刚入门必看！少走弯路！少自我内耗！)](https://www.bilibili.com/video/BV1ZM4m1m785/)
#     ```
#     1. 为什么我用yolov8自带的coco8、coco128训练出来的效果很差？
#     2. 我的数据集很大，机器跑得慢，我是否可以用数据集的百分之10的数据去测试这个改进点是否有效？有效再跑整个数据集？
#     ```
# 5. [深度学习实验部分常见疑问解答三！(怎么判断模型是否收敛？模型过拟合怎么办？)](https://www.bilibili.com/video/BV11S421d76P/)
# 6. [YOLO系列模型训练结果详细解答！(训练过程的一些疑问，该放哪个文件运行出来的结果、参数量计算量在哪里看..等等问题)](https://www.bilibili.com/video/BV11b421J7Vx/)
# 7. [深度学习论文实验中新手非常容易陷入的一个误区：抱着解决xxx问题的心态去做实验](https://www.bilibili.com/video/BV1kkkvYJEHG/)
# 8. [深度学习实验准备-数据集怎么选？有哪些需要注意的点？](https://www.bilibili.com/video/BV11zySYvEhs/)
# 9. [深度学习炼丹必备必看必须知道的小技巧！](https://www.bilibili.com/video/BV1q3SZYsExc/)

# 在20250502更新中，修改保存权重的逻辑，训练结束(注意是正常训练结束后，手动停止的没有)后统一会保存4个模型，
# 分别是best.pt、last.pt、best_fp32.pt、last_fp32.pt，其中不带fp32后缀的是fp16格式保存的，
# 但由于有些模块对fp16非常敏感，会出现后续使用val.py的时候精度为0的情况，这种情况下可以用后缀带fp32去测试。

# 想找到哪些yaml是做轻量化的话可以用get_all_yaml_param_and_flops.py脚本，这个脚本里面有对应的教程视频。

if __name__ == '__main__':
    model = YOLO('ultralytics/cfg/models/v8/yolov8-EIEM-EMBSFPN-RSCD-ultimate.yaml')
    # E:\cursor\yolo3\ultralytics-main\ultralytics\cfg\models\v8\yolov8n-C2f-EMSCP-TADDH.yaml
    
    # model.load('yolov8n.pt') # loading pretrain weights - 设置为False，从头训练
    
    # 开始训练 - 使用优化的专业参数配置
    model.train(
        # 数据集配置
        data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',

        
        # 训练基本参数
        epochs=100,              # 快速测试用1个epoch，正式训练建议50-100
        patience=40,           # 双分类需要更多耐心 (区分火焰和烟雾)
        batch=-1,              # 自动选择最优batch size
        imgsz=640,             # 输入图像尺寸
        
        # 设备设置
        device="0" if os.environ.get('CUDA_VISIBLE_DEVICES') != '-1' else "cpu",
        workers=8,             # 数据加载线程数 (Windows问题时可设为0)
        
        # 项目和名称设置
        project='runs/train',          # 保存到指定的results目录
        name='fire-smoke-dataset-yolov8-EIEM-EMBSFPN-RSCD-ultimate',  # 使用配置名称作为文件夹名
        exist_ok=True,         # 允许覆盖现有结果
        
        # 模型设置
        pretrained=False,      # 不使用预训练权重，从头训练
        
        # 优化器设置 (针对双分类火焰烟雾检测优化)
        optimizer="AdamW",     # 使用AdamW优化器 (比SGD更适合火焰检测)
        lr0=0.0015,           # 适中的初始学习率 (双分类需要更平衡的学习)
        lrf=0.001,            # 降低最终学习率因子 (更细致的收敛)
        momentum=0.937,        # 动量
        weight_decay=0.0005,   # 权重衰减
        
        # 学习率调度
        cos_lr=True,          # 使用余弦学习率调度
        warmup_epochs=2.0,    # 缩短预热 (火焰特征相对明显)
        warmup_momentum=0.8,   # 预热动量
        warmup_bias_lr=0.1,   # 预热偏置学习率
        
        # 其他训练参数
        verbose=True,         # 详细输出
        seed=42,              # 随机种子，确保可重复性
        deterministic=True,    # 确定性训练
        single_cls=False,    # 双类别检测 (火焰+烟雾)
        rect=False,           # 矩形训练
        close_mosaic=5,       # 更早关闭马赛克 (保持火焰完整性)
        resume=False,         # 不恢复训练 (从头开始)
        amp=False,            # 关闭自动混合精度训练 (避免某些模块问题)
        fraction=1.0,         # 使用100%数据
        profile=False,        # 不启用性能分析
        freeze=None,          # 不冻结层
        
        # 数据增强参数 (针对双分类火焰烟雾检测优化)
        hsv_h=0.025,          # 增加色调变化 (区分火焰和烟雾的颜色特征)
        hsv_s=0.8,            # 增加饱和度变化 (火焰饱和度高，烟雾饱和度低)
        hsv_v=0.6,            # 增加亮度变化 (不同光照条件下的火焰和烟雾)
        degrees=15.0,         # 增加旋转 (火焰形状多变)
        translate=0.2,        # 增加平移 (火焰位置变化)
        scale=0.8,            # 增加缩放范围 (火焰大小变化很大)
        shear=0.0,            # 保持剪切为0 (火焰不适合剪切)
        perspective=0.0,      # 保持透视为0 (火焰不适合透视)
        flipud=0.0,           # 保持垂直翻转为0 (火焰方向性)
        fliplr=0.5,           # 保持水平翻转
        mosaic=0.8,           # 适当降低马赛克 (保持火焰完整性)
        mixup=0.0,            # 保持mixup为0 (火焰特征明显)
        copy_paste=0.0,       # 保持复制粘贴为0 (火焰形状特殊)
        
        # 损失权重 (针对双分类火焰烟雾检测优化)
        box=8.0,              # 框回归权重 (火焰形状重要)
        cls=1.0,              # 提高分类权重 (双分类需要区分火焰和烟雾)
        dfl=2.0,              # 提高分布焦点损失 (提高定位精度)
      
        
        # 缓存和其他
        cache=True,          # 使用缓存 

    )