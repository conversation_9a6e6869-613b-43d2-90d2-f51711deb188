import os
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# C2f-EMSCP模型对比分析脚本
# 用于分析不同训练策略的效果

def load_training_results(result_path):
    """加载训练结果数据"""
    results_file = Path(result_path) / "results.csv"
    if results_file.exists():
        return pd.read_csv(results_file)
    return None

def analyze_model_performance():
    """分析模型性能对比"""
    print("🔍 C2f-EMSCP模型性能对比分析")
    print("=" * 50)
    
    # 定义模型路径
    models = {
        "原始训练": "runs/train/fire-smoke-dataset-yolov8n-C2f-EMSCP",
        "简洁优化": "runs/train/C2f-EMSCP-simple", 
        "完整优化": "runs/train/fire-smoke-dataset-yolov8n-C2f-EMSCP-optimized",
        "渐进式-阶段1": "runs/train/C2f-EMSCP-phase1-warmup",
        "渐进式-阶段2": "runs/train/C2f-EMSCP-phase2-optimize", 
        "渐进式-阶段3": "runs/train/C2f-EMSCP-phase3-finetune"
    }
    
    results = {}
    
    # 加载各模型结果
    for name, path in models.items():
        if os.path.exists(path):
            data = load_training_results(path)
            if data is not None:
                # 获取最佳性能指标
                best_epoch = data.loc[data['val/mAP50'].idxmax()]
                results[name] = {
                    'mAP50': best_epoch['val/mAP50'],
                    'mAP50-95': best_epoch['val/mAP50-95'],
                    'Precision': best_epoch['metrics/precision(B)'],
                    'Recall': best_epoch['metrics/recall(B)'],
                    'Best_Epoch': best_epoch['epoch']
                }
                print(f"✅ {name}: mAP50={best_epoch['val/mAP50']:.3f}")
            else:
                print(f"⚠️  {name}: 结果文件未找到")
        else:
            print(f"❌ {name}: 路径不存在")
    
    return results

def create_comparison_chart(results):
    """创建对比图表"""
    if not results:
        print("❌ 没有可用的结果数据")
        return
    
    # 创建DataFrame
    df = pd.DataFrame(results).T
    
    # 创建对比图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('C2f-EMSCP模型训练效果对比', fontsize=16, fontweight='bold')
    
    # mAP50对比
    axes[0, 0].bar(df.index, df['mAP50'], color='skyblue', alpha=0.7)
    axes[0, 0].set_title('mAP50对比')
    axes[0, 0].set_ylabel('mAP50')
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # mAP50-95对比  
    axes[0, 1].bar(df.index, df['mAP50-95'], color='lightgreen', alpha=0.7)
    axes[0, 1].set_title('mAP50-95对比')
    axes[0, 1].set_ylabel('mAP50-95')
    axes[0, 1].tick_params(axis='x', rotation=45)
    
    # Precision vs Recall
    axes[1, 0].scatter(df['Recall'], df['Precision'], s=100, alpha=0.7)
    for i, txt in enumerate(df.index):
        axes[1, 0].annotate(txt, (df['Recall'].iloc[i], df['Precision'].iloc[i]), 
                           xytext=(5, 5), textcoords='offset points', fontsize=8)
    axes[1, 0].set_xlabel('Recall')
    axes[1, 0].set_ylabel('Precision')
    axes[1, 0].set_title('Precision vs Recall')
    
    # 训练轮数对比
    axes[1, 1].bar(df.index, df['Best_Epoch'], color='coral', alpha=0.7)
    axes[1, 1].set_title('最佳模型训练轮数')
    axes[1, 1].set_ylabel('Epochs')
    axes[1, 1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('Improve/c2f-emscp/model_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("📊 对比图表已保存: Improve/c2f-emscp/model_comparison.png")

def generate_report(results):
    """生成分析报告"""
    if not results:
        return
    
    report = []
    report.append("# C2f-EMSCP模型训练效果分析报告\n")
    report.append("## 📊 性能指标对比\n")
    
    # 创建对比表格
    df = pd.DataFrame(results).T
    report.append("| 训练方法 | mAP50 | mAP50-95 | Precision | Recall | 最佳轮数 |")
    report.append("|---------|-------|----------|-----------|--------|----------|")
    
    for name, row in df.iterrows():
        report.append(f"| {name} | {row['mAP50']:.3f} | {row['mAP50-95']:.3f} | "
                     f"{row['Precision']:.3f} | {row['Recall']:.3f} | {int(row['Best_Epoch'])} |")
    
    # 分析最佳模型
    best_map50 = df['mAP50'].idxmax()
    best_map50_95 = df['mAP50-95'].idxmax()
    
    report.append(f"\n## 🏆 最佳模型分析\n")
    report.append(f"- **mAP50最高**: {best_map50} ({df.loc[best_map50, 'mAP50']:.3f})")
    report.append(f"- **mAP50-95最高**: {best_map50_95} ({df.loc[best_map50_95, 'mAP50-95']:.3f})")
    
    # 优化建议
    report.append(f"\n## 💡 优化建议\n")
    if "渐进式-阶段3" in results:
        report.append("- 渐进式训练通常能获得最佳效果")
        report.append("- 建议使用三阶段训练策略")
    report.append("- 继续调优学习率和损失权重")
    report.append("- 考虑增加训练轮数或调整数据增强策略")
    
    # 保存报告
    with open('Improve/c2f-emscp/analysis_report.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("📝 分析报告已保存: Improve/c2f-emscp/analysis_report.md")

if __name__ == '__main__':
    print("🚀 开始C2f-EMSCP模型对比分析...")
    
    # 分析模型性能
    results = analyze_model_performance()
    
    if results:
        # 创建对比图表
        create_comparison_chart(results)
        
        # 生成分析报告
        generate_report(results)
        
        print("\n✅ 分析完成！")
        print("📁 查看结果:")
        print("   - 对比图表: Improve/c2f-emscp/model_comparison.png")
        print("   - 分析报告: Improve/c2f-emscp/analysis_report.md")
    else:
        print("❌ 没有找到训练结果，请先运行训练脚本") 