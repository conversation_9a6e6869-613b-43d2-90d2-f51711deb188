import warnings
warnings.filterwarnings('ignore')
from ultralytics import YOL<PERSON>

if __name__ == '__main__':
    # 使用标准YOLOv8n模型
    model = YOLO('yolov8n.pt')
    
    # 开始训练
    model.train(data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
                epochs=100,
                cache=False,
                imgsz=640,
                batch=16,
                close_mosaic=10,
                workers=0,
                optimizer='Adam',
                device='0',
                patience=50,
                resume=True,
                amp=False,
                project='runs/train',
                name='fire-smoke-dataset-yolov8n-standard',
                ) 