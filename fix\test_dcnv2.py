import warnings
warnings.filterwarnings('ignore')
import torch
import torch.nn as nn
import traceback

def test_torchvision_deform_conv2d():
    """测试torchvision的deform_conv2d操作"""
    try:
        print("🔍 测试torchvision.deform_conv2d操作...")
        
        # 检查torchvision版本
        import torchvision
        print(f"📦 torchvision版本: {torchvision.__version__}")
        
        # 创建测试数据
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  设备: {device}")
        
        # 小的测试尺寸
        batch_size = 1
        in_channels = 64
        out_channels = 64
        height, width = 32, 32
        kernel_size = 3
        
        # 创建测试输入
        x = torch.randn(batch_size, in_channels, height, width).to(device)
        print(f"✅ 输入张量创建成功: {x.shape}")
        
        # 创建权重
        weight = torch.randn(out_channels, in_channels, kernel_size, kernel_size).to(device)
        bias = torch.randn(out_channels).to(device)
        print(f"✅ 权重张量创建成功: {weight.shape}")
        
        # 创建offset和mask
        offset_channels = 2 * kernel_size * kernel_size
        mask_channels = kernel_size * kernel_size
        
        offset = torch.randn(batch_size, offset_channels, height, width).to(device)
        mask = torch.sigmoid(torch.randn(batch_size, mask_channels, height, width)).to(device)
        print(f"✅ offset和mask张量创建成功: {offset.shape}, {mask.shape}")
        
        # 测试deform_conv2d操作
        print("🚀 开始测试deform_conv2d操作...")
        
        result = torch.ops.torchvision.deform_conv2d(
            x,
            weight,
            offset,
            mask,
            bias,
            1, 1,  # stride
            1, 1,  # padding
            1, 1,  # dilation
            1,     # groups
            1,     # deformable_groups
            True   # use_mask
        )
        
        print(f"✅ deform_conv2d操作成功! 输出形状: {result.shape}")
        return True
        
    except Exception as e:
        print(f"❌ deform_conv2d操作失败: {e}")
        traceback.print_exc()
        return False

def test_dcnv2_module():
    """测试DCNv2模块"""
    try:
        print("\n🔍 测试DCNv2模块...")
        
        # 导入DCNv2模块
        from ultralytics.nn.extra_modules.block import DCNv2
        
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        
        # 创建DCNv2模块
        dcnv2 = DCNv2(64, 64, 3).to(device)
        print(f"✅ DCNv2模块创建成功")
        
        # 创建测试输入
        x = torch.randn(1, 64, 32, 32).to(device)
        print(f"✅ 测试输入创建成功: {x.shape}")
        
        # 前向传播
        print("🚀 开始DCNv2前向传播...")
        with torch.no_grad():
            output = dcnv2(x)
        
        print(f"✅ DCNv2前向传播成功! 输出形状: {output.shape}")
        return True
        
    except Exception as e:
        print(f"❌ DCNv2模块测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🧪 开始DCNv2兼容性测试...")
    
    # 测试基础操作
    basic_test = test_torchvision_deform_conv2d()
    
    # 测试DCNv2模块
    module_test = test_dcnv2_module()
    
    print("\n📋 测试结果:")
    print(f"  - torchvision.deform_conv2d: {'✅ 通过' if basic_test else '❌ 失败'}")
    print(f"  - DCNv2模块: {'✅ 通过' if module_test else '❌ 失败'}")
    
    if basic_test and module_test:
        print("\n🎉 所有测试都通过！DCNv2模块应该可以正常工作。")
    else:
        print("\n⚠️  DCNv2模块存在兼容性问题，建议使用标准YOLOv8模型或其他改进模块。") 