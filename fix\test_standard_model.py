import warnings
warnings.filterwarnings('ignore')
from ultralytics import YOL<PERSON>

if __name__ == '__main__':
    try:
        # 使用标准YOLOv8n模型
        model = YOLO('yolov8n.pt')  # 使用预训练的标准模型
        
        print("✅ 标准YOLOv8n模型加载成功")
        
        # 开始训练
        print("🚀 开始训练...")
        model.train(
            data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            epochs=1,  # 只训练1个epoch来测试
            imgsz=640,
            batch=1,   # 使用小batch size
            workers=0,
            device='0',
            verbose=True,
            project='runs/train',
            name='test-standard-model',
        )
        print("✅ 训练完成")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc() 