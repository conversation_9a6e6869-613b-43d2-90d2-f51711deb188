import warnings, os
import torch
import torch.nn as nn
import multiprocessing as mp
import platform
warnings.filterwarnings('ignore')

# Windows多进程修复 - 必须在导入YOLO之前
if __name__ == '__main__':
    mp.set_start_method('spawn', force=True)

from ultralytics import YOLO

# C2f-EMSCP模型优化训练脚本 - 简洁版本
# 基于BiFPN对比分析的针对性优化
# 完全复刻BiFPN clean版本的结构和逻辑

if __name__ == '__main__':
    # Windows多进程修复
    mp.set_start_method('spawn', force=True)
    
    print("=" * 60)
    print("C2f-EMSCP模型优化训练脚本")
    print("=" * 60)
    
    # 1. 模型初始化
    model = YOLO('ultralytics/cfg/models/v8/yolov8-C2f-EMSCP.yaml')
    
    # 2. 关键优化：加载预训练权重
    print("加载预训练权重...")
    model.load('yolov8n.pt')
    
    # 3. 优化训练参数
    print("开始优化训练...")
    model.train(
        # === 数据集配置 ===
        data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
        
        # === 基本训练参数 ===
        epochs=100,              # 增加训练轮数
        patience=60,             # 增加耐心值  
        batch=-1,                # 自动选择batch size
        imgsz=640,
        
        # === 设备设置 ===
        device="0" if os.environ.get('CUDA_VISIBLE_DEVICES') != '-1' else "cpu",
        workers=2 if platform.system() == 'Windows' else 8,  # Windows缓存兼容性优化
        
        # === 项目设置 ===
        project='runs/train',
        name='fire-smoke-dataset-yolov8n-C2f-EMSCP-optimized',
        exist_ok=True,
        
        # === 模型设置 ===
        pretrained=True,         # 使用预训练权重
        
        # === 优化器设置 ===
        optimizer="AdamW",
        lr0=0.001,              # 降低初始学习率 (原0.0015)
        lrf=0.0001,             # 更低的最终学习率 (原0.001)
        momentum=0.937,
        weight_decay=0.0008,    # 增加权重衰减 (原0.0005)
        
        # === 学习率调度 ===
        cos_lr=True,            # 余弦学习率调度
        warmup_epochs=5.0,      # 增加预热轮数 (原2.0)
        warmup_momentum=0.8,
        warmup_bias_lr=0.01,    # 降低预热偏置学习率 (原0.1)
        
        # === 损失权重优化 - 核心优化点 ===
        box=7.5,                # 适当降低框损失权重 (原8.0)
        cls=0.5,                # 显著降低分类损失权重 (原1.0)
        dfl=1.5,                # 适当降低DFL损失权重 (原2.0)
        
        # === 数据增强优化 ===
        hsv_h=0.015,            # 降低色调变化 (原0.025)
        hsv_s=0.7,              # 降低饱和度变化 (原0.8)
        hsv_v=0.4,              # 降低亮度变化 (原0.6)
        degrees=10.0,           # 降低旋转角度 (原15.0)
        translate=0.1,          # 降低平移 (原0.2)
        scale=0.6,              # 降低缩放范围 (原0.8)
        shear=0.0,
        perspective=0.0,
        flipud=0.0,
        fliplr=0.5,
        mosaic=0.6,             # 降低马赛克概率 (原0.8)
        mixup=0.0,
        copy_paste=0.0,
        
        # === C2f-EMSCP特殊优化 ===
        close_mosaic=15,        # 延长马赛克关闭时间 (原5)
        
        # === 其他优化设置 ===
        verbose=True,
        seed=42,
        deterministic=True,
        single_cls=False,
        rect=False,
        resume=False,
        amp=True,               # 启用混合精度
        fraction=1.0,
        profile=False,
        freeze=None,
        cache=True,  # 开启缓存加速
        val=True,
        plots=True,
        save=True,
        save_period=10,
        
        # === 高级设置 ===
        dropout=0.0,
        label_smoothing=0.0,
        # ema=True,               # 移除：无效参数
        
        # === 验证设置 ===
        # val_period=1,           # 移除：无效参数
        
        # === 内存和计算优化 ===
        # pin_memory=True,        # 移除：无效参数
        # persistent_workers=True,# 移除：无效参数
        multi_scale=False,
        
        # === 早停设置 ===
        # min_lr=1e-6,           # 移除：无效参数
        
        # === 其他稳定性设置 ===
        half=False,             # 不使用半精度保持稳定
        # sync_bn=False,         # 移除：无效参数
        
        # === 任务设置 ===
        task='detect',
        mode='train',
        
        # === NMS设置 ===
        iou=0.6,
        max_det=300,
        # agnostic_nms=False,    # 移除：无效参数
        
        # === 数据加载优化 ===
        # shuffle=True,          # 移除：无效参数
    )
    
    print("=" * 60)
    print("C2f-EMSCP模型优化训练完成！")
    print("=" * 60)
    print("主要优化点总结：")
    print("1. 🔧 降低分类损失权重: 1.0 → 0.5")
    print("2. ⏱️ 增加预热轮数: 2 → 5")
    print("3. 📉 降低初始学习率: 0.0015 → 0.001")
    print("4. 🔒 增加权重衰减: 0.0005 → 0.0008")
    print("5. 🎨 调整数据增强参数，降低过度增强")
    print("6. 🕐 延长马赛克关闭时间: 5 → 15轮")
    print("7. 🎯 使用预训练权重进行微调")
    print("8. 📈 增加训练轮数和耐心值")
    print("9. 🔄 启用混合精度训练")
    print("10. 💾 优化内存和计算设置")
    print("11. 🖥️ Windows系统兼容性优化")
    print("12. ✅ 移除无效参数确保兼容性")
    print("=" * 60) 