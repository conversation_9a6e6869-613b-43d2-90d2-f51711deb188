import warnings, os
import torch
import gc
import psutil
warnings.filterwarnings('ignore')

# 强制清理内存
def clean_memory():
    """强制清理内存和GPU缓存"""
    print("🧹 清理内存和GPU缓存...")
    
    # 清理Python垃圾回收
    gc.collect()
    
    # 清理GPU缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
    
    # 显示内存状态
    memory = psutil.virtual_memory()
    print(f"内存使用率: {memory.percent}%")
    if torch.cuda.is_available():
        print(f"GPU显存使用: {torch.cuda.memory_allocated(0) / (1024**3):.1f}GB")

if __name__ == '__main__':
    print("⚡ TADDH立即速度修复脚本")
    print("针对内存瓶颈的快速优化")
    print("=" * 50)
    
    # 清理内存
    clean_memory()
    
    from ultralytics import YOLO
    
    # 内存优化设置
    memory = psutil.virtual_memory()
    print(f"当前内存使用: {memory.percent}%")
    
    if memory.percent > 80:
        print("⚠️  检测到内存不足，使用内存优化配置")
        workers = 2  # 减少workers
        cache_setting = True  # 使用磁盘缓存而非RAM缓存
        batch_size = 16  # 固定较小batch
        plots = False  # 关闭绘图
    else:
        print("✅ 内存充足，使用高性能配置") 
        workers = 6
        cache_setting = "ram"
        batch_size = -1
        plots = True
    
    # 模型初始化
    model = YOLO('ultralytics/cfg/models/v8/yolov8n-TADDH.yaml')
    model.load('yolov8n.pt')
    
    print(f"🚀 使用优化配置: workers={workers}, cache={cache_setting}, batch={batch_size}")
    
    model.train(
        # 基础配置
        data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
        epochs=100,
        patience=50,             # 减少耐心值提升收敛速度
        batch=batch_size,
        imgsz=640,
        
        # 内存优化设置
        device="0",
        workers=workers,         # 内存优化的workers数量
        cache=cache_setting,     # 智能缓存策略
        
        # 项目设置
        project='runs/train',
        name='TADDH-memory-optimized-fast',
        exist_ok=True,
        
        # 模型设置
        pretrained=True,
        
        # 速度优化的学习率设置
        optimizer="AdamW",
        lr0=0.001,               # 稍微提高学习率加快收敛
        lrf=0.0001,
        momentum=0.937,
        weight_decay=0.0008,
        
        # 快速收敛设置
        cos_lr=True,
        warmup_epochs=3.0,       # 减少预热轮数
        warmup_momentum=0.8,
        warmup_bias_lr=0.01,
        
        # 保持效果的损失权重
        box=7.0,
        cls=0.4,
        dfl=1.2,
        
        # 速度优化的数据增强 - 减少计算量
        hsv_h=0.01,              # 大幅减少HSV调整
        hsv_s=0.5,
        hsv_v=0.2,
        degrees=3.0,             # 减少旋转计算
        translate=0.03,          # 减少平移计算
        scale=0.2,               # 减少缩放计算
        shear=0.0,               # 关闭剪切变换
        perspective=0.0,         # 关闭透视变换
        flipud=0.0,              # 关闭垂直翻转
        fliplr=0.5,              # 保留水平翻转
        mosaic=0.3,              # 大幅减少马赛克概率
        mixup=0.0,               # 关闭mixup
        copy_paste=0.0,          # 关闭复制粘贴
        close_mosaic=8,          # 更早关闭马赛克
        
        # 内存和速度优化
        amp=True,                # 启用混合精度
        rect=True,               # 启用矩形训练减少padding
        single_cls=False,
        multi_scale=False,       # 关闭多尺度训练
        
        # 输出优化
        val=True,
        plots=plots,             # 根据内存情况决定是否绘图
        save_json=False,         # 关闭JSON保存
        save_txt=False,          # 关闭txt保存
        
        # 保存优化 
        save=True,
        save_period=25,          # 减少保存频率
        
        # 其他速度优化
        verbose=True,
        seed=42,
        deterministic=False,     # 关闭确定性提升速度
        profile=False,           # 关闭性能分析
        overlap_mask=True,       # 启用重叠掩码优化
        mask_ratio=4,            # 掩码比例
        
        # 验证优化
        val_period=1,            # 每轮验证
        
        # 内存管理
        pin_memory=True,         # GPU内存锁定
    )
    
    print("✅ 内存优化训练完成！")
    print("📈 训练速度应该有显著提升")
    print("💾 内存使用得到有效控制") 