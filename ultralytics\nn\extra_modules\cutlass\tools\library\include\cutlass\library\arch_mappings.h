/***************************************************************************************************
 * Copyright (c) 2020, NVIDIA CORPORATION.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 *modification, are permitted provided that the following conditions are met:
 *     * Redistributions of source code must retain the above copyright notice,
 *this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above copyright
 *notice, this list of conditions and the following disclaimer in the
 *documentation and/or other materials provided with the distribution.
 *     * Neither the name of the NVIDIA CORPORATION nor the names of its
 *contributors may be used to endorse or promote products derived from this
 *software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 *AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 *IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 *DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE FOR ANY DIRECT,
 *INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 *OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TOR (INCLUDING
 *NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 *EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 **************************************************************************************************/
/*!
  \file

  \brief CUTLASS Library is an object-oriented approach to managing operations
  implemented by CUTLASS.

  Generally,

    description   - compile-time constant parameters used to instantiate an
  operation

    configuration - runtime parameters with computationally expensive
  initialization

    arguments     - runtime parameters that may be passed to an initialized
  operation with low computational overhead
*/

#pragma once

#include "cutlass/arch/mma.h"
#include "cutlass/arch/arch.h"

/////////////////////////////////////////////////////////////////////////////////////////////////

namespace cutlass {
namespace library {

/////////////////////////////////////////////////////////////////////////////////////////////////

template <typename ArchTag, typename OperatorClass>
struct ArchMap;

template <>
struct ArchMap<arch::Sm50, arch::OpClassSimt> {
    static int const kMin = 50;
    static int const kMax = 1024;
};

template <>
struct ArchMap<arch::Sm60, arch::OpClassSimt> {
    static int const kMin = 60;
    static int const kMax = 1024;
};

template <>
struct ArchMap<arch::Sm61, arch::OpClassSimt> {
    static int const kMin = 61;
    static int const kMax = 1024;
};

template <>
struct ArchMap<arch::Sm70, arch::OpClassWmmaTensorOp> {
    static int const kMin = 70;
    static int const kMax = 1024;
};

template <>
struct ArchMap<arch::Sm70, arch::OpClassTensorOp> {
    static int const kMin = 70;
    static int const kMax = 75;
};

template <typename OperatorClass>
struct ArchMap<arch::Sm75, OperatorClass> {
    static int const kMin = 75;
    static int const kMax = 1024;
};

template <typename OperatorClass>
struct ArchMap<arch::Sm80, OperatorClass> {
    static int const kMin = 80;
    static int const kMax = 1024;
};

template <typename OperatorClass>
struct ArchMap<arch::Sm86, OperatorClass> {
    static int const kMin = 86;
    static int const kMax = 1024;
};

/////////////////////////////////////////////////////////////////////////////////////////////////

}  // namespace library
}  // namespace cutlass

/////////////////////////////////////////////////////////////////////////////////////////////////
