# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024, 6]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, SCDown, [512, 3, 2]] # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, SCDown, [1024, 3, 2]] # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SPPF, [1024, 5]] # 9
  - [-1, 1, PSA, [1024]] # 10

# YOLOv10.0n head
head:
  # Semantic Collecting
  - [0, 1, nn.AvgPool2d, [8, 8, 0]] # 11
  - [2, 1, nn.AvgPool2d, [4, 4, 0]] # 12
  - [4, 1, nn.AvgPool2d, [2, 2, 0]] # 13
  - [10, 1, nn.Upsample, [None, 2, 'nearest']] # 14
  - [[11, 12, 13, 6, 14], 1, Concat, [1]]  # cat 15

  # Hypergraph Computation
  - [-1, 1, Conv, [512, 1, 1]] # 16
  - [-1, 1, HyperComputeModule, [512]] # 17
  - [-1, 3, MANet, [512, True, 2, 3]] # 18

  # Semantic Collecting
  - [-1, 1, nn.AvgPool2d, [2, 2, 0]] # 19
  - [[-1, 10], 1, Concat, [1]]  # cat 20
  - [-1, 1, Conv, [1024, 1, 1]] # 21 P5

  - [[18, 6], 1, Concat, [1]] # cat backbone P4
  - [-1, 3, C2f, [512]] # 23

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 4], 1, Concat, [1]] # cat backbone P3
  - [-1, 3, C2f, [256]] # 26 (P3/8-small)

  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 23], 1, Concat, [1]] # cat head P4
  - [-1, 3, C2f, [512]] # 29 (P4/16-medium)

  - [-1, 1, SCDown, [512, 3, 2]]
  - [[-1, 21], 1, Concat, [1]] # cat head P5
  - [-1, 3, C2fCIB, [1024, True, True]] # 32 (P5/32-large)

  - [[26, 29, 32], 1, v10Detect, [nc]] # Detect(P3, P4, P5)
