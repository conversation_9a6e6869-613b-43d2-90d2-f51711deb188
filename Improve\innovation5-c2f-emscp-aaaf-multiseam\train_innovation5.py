#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 创新点5训练脚本
C2f-EMSCP-AAAF + MultiSEAMHead (多尺度空间-通道增强注意力检测头)

基于Innovation1最佳配置 + MultiSEAM检测头
目标：超越Innovation1的mAP50=0.77564
"""

import sys
import os
import warnings
import torch
from pathlib import Path
from datetime import datetime

warnings.filterwarnings('ignore')

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from ultralytics import YOLO
from ultralytics.utils import LOGGER

# 注册自定义模块
from modules import EMSConvP, AdaptiveAttentionFusion, Bottleneck_EMSCP_AAF, C2f_EMSCP_AAF

# 将模块添加到ultralytics命名空间
import ultralytics.nn.extra_modules.block as block_module
block_module.EMSConvP = EMSConvP
block_module.AdaptiveAttentionFusion = AdaptiveAttentionFusion
block_module.Bottleneck_EMSCP_AAF = Bottleneck_EMSCP_AAF
block_module.C2f_EMSCP_AAF = C2f_EMSCP_AAF

# 更新__all__列表
if hasattr(block_module, '__all__'):
    block_module.__all__.extend(['EMSConvP', 'AdaptiveAttentionFusion', 'Bottleneck_EMSCP_AAF', 'C2f_EMSCP_AAF'])
else:
    block_module.__all__ = ['EMSConvP', 'AdaptiveAttentionFusion', 'Bottleneck_EMSCP_AAF', 'C2f_EMSCP_AAF']

# 同时注册到全局命名空间
import ultralytics.nn.tasks as tasks_module
tasks_module.EMSConvP = EMSConvP
tasks_module.AdaptiveAttentionFusion = AdaptiveAttentionFusion
tasks_module.Bottleneck_EMSCP_AAF = Bottleneck_EMSCP_AAF
tasks_module.C2f_EMSCP_AAF = C2f_EMSCP_AAF

def check_environment():
    """检查环境配置"""
    print("🔧 检查环境配置...")
    
    # 检查CUDA
    if torch.cuda.is_available():
        print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
        print(f"📊 显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    else:
        print("⚠️ 使用CPU训练")
    
    # 检查数据集
    dataset_path = os.path.join(project_root, "ultralytics/cfg/datasets/fire-smoke-dataset.yaml")
    if os.path.exists(dataset_path):
        print(f"✅ 数据集: {dataset_path}")
    else:
        raise FileNotFoundError(f"❌ 数据集不存在: {dataset_path}")

def get_innovation5_config():
    """获取创新点5的优化配置"""
    return {
        # === 数据集 ===
        "data": "ultralytics/cfg/datasets/fire-smoke-dataset.yaml",
        
        # === 训练设置 ===
        "epochs": 100,                # 快速测试轮数
        "batch": 24,                # 基于innovation1最佳配置
        "imgsz": 640,               # 标准图像尺寸
        "device": "0" if torch.cuda.is_available() else "cpu",
        "workers": 8,               # 优化的工作线程数
        
        # === 项目设置 ===
        "project": "Improve/innovation5-c2f-emscp-aaaf-multiseam/runs",
        "name": f"innovation5_multiseam_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "exist_ok": True,
        
        # === 优化器设置 (基于innovation1最佳配置) ===
        "optimizer": "AdamW",
        "lr0": 0.0006,              # innovation1最佳学习率
        "lrf": 0.0001,              # 最终学习率因子
        "momentum": 0.937,
        "weight_decay": 0.0008,
        "cos_lr": True,             # 余弦学习率调度
        "warmup_epochs": 3.0,
        "warmup_momentum": 0.8,
        "warmup_bias_lr": 0.1,
        
        # === 损失权重 (基于innovation1最佳配置) ===
        "box": 8.0,                 # innovation1最佳边界框权重
        "cls": 0.5,                 # innovation1最佳分类权重
        "dfl": 1.5,                 # 分布焦点损失权重
        
        # === 数据增强 (火焰烟雾优化) ===
        "hsv_h": 0.008,             # 色调增强
        "hsv_s": 0.5,               # 饱和度增强
        "hsv_v": 0.3,               # 明度增强
        "degrees": 6.0,             # 旋转角度
        "translate": 0.06,          # 平移
        "scale": 0.4,               # 缩放
        "shear": 0.0,               # 禁用剪切
        "perspective": 0.0,         # 禁用透视变换
        "flipud": 0.0,              # 垂直翻转
        "fliplr": 0.3,              # 水平翻转
        "mosaic": 0.4,              # 马赛克增强
        "mixup": 0.0,               # 禁用混合增强
        "copy_paste": 0.0,          # 禁用复制粘贴
        "close_mosaic": 20,         # 关闭马赛克的轮数
        
        # === 训练优化 ===
        "amp": True,                # 自动混合精度
        "cache": False,             # 不缓存图像
        "multi_scale": False,       # 不使用多尺度训练
        "patience": 20,             # 早停耐心值
        "save_period": 10,          # 保存周期
        
        # === 验证设置 ===
        "val": True,
        "plots": True,              # 生成训练图表
        "verbose": True,
    }

def train_innovation5():
    """训练创新点5模型"""
    print("🚀 开始创新点5训练...")
    print("🎯 目标: 超越Innovation1的mAP50=0.77564")
    
    # 获取配置
    config = get_innovation5_config()
    
    print("\n🎯 创新点5配置参数:")
    print(f"  核心特色: C2f-EMSCP-AAAF + MultiSEAMHead")
    print(f"  学习率: {config['lr0']}")
    print(f"  分类权重: {config['cls']}")
    print(f"  边界框权重: {config['box']}")
    print(f"  批次大小: {config['batch']}")
    print(f"  训练轮数: {config['epochs']}")
    print(f"  MultiSEAM特色: 多尺度空间-通道增强注意力")
    print()
    
    try:
        # 创建模型
        print("📦 加载创新点5模型...")
        config_path = os.path.join(os.path.dirname(__file__), 'config.yaml')
        model = YOLO(config_path)
        
        print("✅ 模型加载成功")
        print(f"📊 模型信息:")
        print(f"   - 参数量: {sum(p.numel() for p in model.model.parameters()):,}")
        print(f"   - 可训练参数: {sum(p.numel() for p in model.model.parameters() if p.requires_grad):,}")
        
        # 开始训练
        print("\n🔥 开始训练...")
        results = model.train(**config)
        
        print("✅ 训练完成!")
        return results
        
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_results(results):
    """分析训练结果"""
    print("\n📊 训练结果分析:")
    print("=" * 50)
    
    try:
        if hasattr(results, 'results_dict'):
            metrics = results.results_dict
            
            # 获取关键指标
            mAP50 = metrics.get('metrics/mAP50(B)', 0)
            mAP50_95 = metrics.get('metrics/mAP50-95(B)', 0)
            precision = metrics.get('metrics/precision(B)', 0)
            recall = metrics.get('metrics/recall(B)', 0)
            
            print(f"📈 最终性能指标:")
            print(f"  mAP50: {mAP50:.5f}")
            print(f"  mAP50-95: {mAP50_95:.5f}")
            print(f"  Precision: {precision:.5f}")
            print(f"  Recall: {recall:.5f}")
            
            # 与基准对比
            baseline_mAP50 = 0.75445      # 基准模型
            innovation1_mAP50 = 0.77564   # Innovation1最佳
            innovation2_mAP50 = 0.73641   # Innovation2结果
            c2f_emscp_mAP50 = 0.78026     # C2f-EMSCP最佳
            
            print(f"\n🎯 性能对比:")
            print(f"  vs 基准模型 (0.75445): {(mAP50 - baseline_mAP50) * 100:+.2f}%")
            print(f"  vs Innovation1 (0.77564): {(mAP50 - innovation1_mAP50) * 100:+.2f}%")
            print(f"  vs Innovation2 (0.73641): {(mAP50 - innovation2_mAP50) * 100:+.2f}%")
            print(f"  vs C2f-EMSCP (0.78026): {(mAP50 - c2f_emscp_mAP50) * 100:+.2f}%")
            
            # 性能评估
            if mAP50 > c2f_emscp_mAP50:
                print("🚀 创造新的最佳记录!")
            elif mAP50 > innovation1_mAP50:
                print("🎉 成功超越Innovation1!")
            elif mAP50 > baseline_mAP50:
                print("✅ 超越基准模型!")
            else:
                print("⚠️ 性能未达预期，建议进一步调优")
            
            # MultiSEAM效果分析
            print(f"\n🔍 MultiSEAM检测头效果分析:")
            print(f"  多尺度SEAM模块应用于P3/P4/P5层")
            print(f"  空间-通道双重注意力机制增强特征表达")
            print(f"  多尺度特征融合提升不同尺寸目标检测")
            
        else:
            print("⚠️ 无法获取详细结果")
            
    except Exception as e:
        print(f"❌ 结果分析出错: {e}")

def main():
    """主函数"""
    print("🎯 创新点5: C2f-EMSCP-AAAF + MultiSEAMHead")
    print("基于Innovation1最佳配置 + 多尺度空间-通道增强注意力检测头")
    print("=" * 70)
    
    try:
        # 1. 环境检查
        check_environment()
        print()
        
        # 2. 确认训练
        print("📋 训练配置:")
        config = get_innovation5_config()
        print(f"  模型: YOLOv8n + C2f-EMSCP-AAAF + MultiSEAMHead")
        print(f"  创新特性: 自适应注意力聚合 + 多尺度SEAM")
        print(f"  训练轮数: {config['epochs']}")
        print(f"  批次大小: {config['batch']}")
        print(f"  学习率: {config['lr0']}")
        print(f"  预计时间: ~{config['epochs'] * 0.025:.1f}小时")
        
        confirm = input("\n是否开始训练? (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ 训练已取消")
            return
        
        # 3. 开始训练
        results = train_innovation5()
        
        # 4. 分析结果
        if results:
            analyze_results(results)
        
        print("\n" + "=" * 70)
        print("🎉 创新点5训练完成!")
        print("📁 结果保存在: Improve/innovation5-c2f-emscp-aaaf-multiseam/runs/")
        print("📋 下一步:")
        print("  1. 查看训练图表和MultiSEAM效果")
        print("  2. 对比Innovation1的性能差异")
        print("  3. 分析多尺度注意力的贡献")
        
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
