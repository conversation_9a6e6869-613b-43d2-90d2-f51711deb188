---
description: Discover detailed instructions for building various Segment Anything Model (SAM) architectures with Ultralytics, including SAM ViT and Mobile-SAM.
keywords: Ultralytics, SAM model, Segment Anything Model, SAM ViT, Mobile-SAM, model building, deep learning, AI
---

# Reference for `ultralytics/models/sam/build.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/build.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/build.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/build.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.sam.build.build_sam_vit_h

<br><br>

## ::: ultralytics.models.sam.build.build_sam_vit_l

<br><br>

## ::: ultralytics.models.sam.build.build_sam_vit_b

<br><br>

## ::: ultralytics.models.sam.build.build_mobile_sam

<br><br>

## ::: ultralytics.models.sam.build._build_sam

<br><br>

## ::: ultralytics.models.sam.build.build_sam

<br><br>
