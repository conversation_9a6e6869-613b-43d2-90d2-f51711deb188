#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 创新点2优化训练脚本
C2f-EMSCP + 边缘信息增强特征金字塔 (EIFPN)

基于innovation1的最佳配置进行优化
目标：超越innovation1的mAP50=0.77564
"""

import sys
import os
import warnings
import torch
from pathlib import Path
from datetime import datetime

warnings.filterwarnings('ignore')

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from ultralytics import YOLO
from ultralytics.utils import LOGGER

# 注册自定义模块
from modules import SobelConv, EIEM, Bottleneck_EMSCP_EIEM, C2f_EMSCP_EIEM, EdgeEnhancedFusion

# 将模块添加到ultralytics命名空间
import ultralytics.nn.extra_modules.block as block_module
block_module.SobelConv = SobelConv
block_module.EIEM = EIEM
block_module.Bottleneck_EMSCP_EIEM = Bottleneck_EMSCP_EIEM
block_module.C2f_EMSCP_EIEM = C2f_EMSCP_EIEM
block_module.EdgeEnhancedFusion = EdgeEnhancedFusion

# 更新__all__列表
if hasattr(block_module, '__all__'):
    block_module.__all__.extend(['SobelConv', 'EIEM', 'Bottleneck_EMSCP_EIEM', 'C2f_EMSCP_EIEM', 'EdgeEnhancedFusion'])

def check_environment():
    """检查环境配置"""
    print("🔧 检查环境配置...")
    
    # 检查CUDA
    if torch.cuda.is_available():
        print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
        print(f"📊 显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    else:
        print("⚠️ 使用CPU训练")
    
    # 检查数据集
    dataset_path = "ultralytics/cfg/datasets/fire-smoke-dataset.yaml"
    if os.path.exists(dataset_path):
        print(f"✅ 数据集: {dataset_path}")
    else:
        raise FileNotFoundError(f"❌ 数据集不存在: {dataset_path}")

def get_optimized_config():
    """获取基于innovation1最佳配置的优化参数"""
    return {
        # === 数据集 ===
        "data": "ultralytics/cfg/datasets/fire-smoke-dataset.yaml",
        
        # === 训练设置 ===
        "epochs": 100,              # 充足的训练轮数
        "batch": 24,                # 基于innovation1最佳配置
        "imgsz": 640,               # 标准图像尺寸
        "device": "0" if torch.cuda.is_available() else "cpu",
        "workers": 8,               # 优化的工作线程数
        
        # === 项目设置 ===
        "project": "Improve/innovation2-c2f-emscp-eifpn/runs",
        "name": f"innovation2_optimized_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "exist_ok": True,
        
        # === 优化器设置 (基于innovation1最佳配置) ===
        "optimizer": "AdamW",
        "lr0": 0.0006,              # innovation1最佳学习率
        "lrf": 0.0001,              # 最终学习率因子
        "momentum": 0.937,
        "weight_decay": 0.0008,
        "cos_lr": True,             # 余弦学习率调度
        "warmup_epochs": 3.0,
        "warmup_momentum": 0.8,
        "warmup_bias_lr": 0.1,
        
        # === 损失权重 (基于innovation1最佳配置) ===
        "box": 8.0,                 # innovation1最佳边界框权重
        "cls": 0.5,                 # innovation1最佳分类权重
        "dfl": 1.5,                 # 分布焦点损失权重
        
        # === 数据增强 (火焰烟雾优化 + 边缘保护) ===
        "hsv_h": 0.008,             # 降低色调增强，保护边缘信息
        "hsv_s": 0.5,               # 适度饱和度增强
        "hsv_v": 0.3,               # 明度增强
        "degrees": 6.0,             # 减少旋转，保护边缘特征
        "translate": 0.06,          # 减少平移，保护边缘
        "scale": 0.4,               # 适度缩放
        "shear": 0.0,               # 禁用剪切，保护边缘
        "perspective": 0.0,         # 禁用透视变换
        "flipud": 0.0,              # 垂直翻转
        "fliplr": 0.3,              # 减少水平翻转
        "mosaic": 0.4,              # 减少马赛克，保护边缘
        "mixup": 0.0,               # 禁用混合增强
        "copy_paste": 0.0,          # 禁用复制粘贴
        "close_mosaic": 20,         # 更早关闭马赛克
        
        # === 训练优化 ===
        "amp": True,                # 自动混合精度
        "cache": False,             # 不缓存图像
        "multi_scale": False,       # 不使用多尺度训练
        "patience": 20,             # 增加早停耐心值
        "save_period": 10,          # 保存周期
        
        # === 验证设置 ===
        "val": True,
        "plots": True,              # 生成训练图表
        "verbose": True,
    }

def train_innovation2():
    """训练创新点2模型"""
    print("🚀 开始创新点2训练...")
    print("🎯 目标: 超越innovation1的mAP50=0.77564")
    
    # 获取优化配置
    config = get_optimized_config()
    
    print("\n🎯 优化配置参数:")
    print(f"  学习率: {config['lr0']}")
    print(f"  分类权重: {config['cls']}")
    print(f"  边界框权重: {config['box']}")
    print(f"  批次大小: {config['batch']}")
    print(f"  训练轮数: {config['epochs']}")
    print(f"  边缘保护增强: hsv_h={config['hsv_h']}, degrees={config['degrees']}")
    print()
    
    try:
        # 创建模型
        print("📦 加载创新点2模型...")
        config_path = os.path.join(os.path.dirname(__file__), 'config.yaml')
        model = YOLO(config_path)
        
        print("✅ 模型加载成功")
        print(f"📊 模型信息:")
        print(f"   - 参数量: {sum(p.numel() for p in model.model.parameters()):,}")
        print(f"   - 可训练参数: {sum(p.numel() for p in model.model.parameters() if p.requires_grad):,}")
        
        # 开始训练
        print("\n🔥 开始训练...")
        results = model.train(**config)
        
        print("✅ 训练完成!")
        return results
        
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_results(results):
    """分析训练结果"""
    print("\n📊 训练结果分析:")
    print("=" * 50)
    
    try:
        if hasattr(results, 'results_dict'):
            metrics = results.results_dict
            
            # 获取关键指标
            mAP50 = metrics.get('metrics/mAP50(B)', 0)
            mAP50_95 = metrics.get('metrics/mAP50-95(B)', 0)
            precision = metrics.get('metrics/precision(B)', 0)
            recall = metrics.get('metrics/recall(B)', 0)
            
            print(f"📈 最终性能指标:")
            print(f"  mAP50: {mAP50:.5f}")
            print(f"  mAP50-95: {mAP50_95:.5f}")
            print(f"  Precision: {precision:.5f}")
            print(f"  Recall: {recall:.5f}")
            
            # 与基准对比
            baseline_mAP50 = 0.75445      # 基准模型
            innovation1_mAP50 = 0.77564   # Innovation1最佳
            bifpn_mAP50 = 0.76862         # BiFPN增强
            c2f_emscp_mAP50 = 0.78026     # C2f-EMSCP最佳
            
            print(f"\n🎯 性能对比:")
            print(f"  vs 基准模型 (0.75445): {(mAP50 - baseline_mAP50) * 100:+.2f}%")
            print(f"  vs Innovation1 (0.77564): {(mAP50 - innovation1_mAP50) * 100:+.2f}%")
            print(f"  vs BiFPN (0.76862): {(mAP50 - bifpn_mAP50) * 100:+.2f}%")
            print(f"  vs C2f-EMSCP (0.78026): {(mAP50 - c2f_emscp_mAP50) * 100:+.2f}%")
            
            # 性能评估
            if mAP50 > c2f_emscp_mAP50:
                print("🚀 创造新的最佳记录!")
            elif mAP50 > innovation1_mAP50:
                print("🎉 成功超越Innovation1!")
            elif mAP50 > bifpn_mAP50:
                print("✅ 超越BiFPN增强版本!")
            elif mAP50 > baseline_mAP50:
                print("📈 超越基准模型!")
            else:
                print("⚠️ 性能未达预期，建议进一步调优")
            
            # 边缘增强效果分析
            print(f"\n🔍 边缘增强效果分析:")
            print(f"  边缘信息增强模块 (EIEM) 应用于P4/16和P5/32层")
            print(f"  Sobel边缘检测增强火焰轮廓识别")
            print(f"  多尺度卷积 (EMSConvP) 适应不同尺度特征")
            
        else:
            print("⚠️ 无法获取详细结果")
            
    except Exception as e:
        print(f"❌ 结果分析出错: {e}")

def main():
    """主函数"""
    print("🎯 创新点2: C2f-EMSCP + 边缘信息增强特征金字塔 (EIFPN)")
    print("基于Innovation1最佳配置的优化训练")
    print("=" * 70)
    
    try:
        # 1. 环境检查
        check_environment()
        print()
        
        # 2. 确认训练
        print("📋 训练配置:")
        config = get_optimized_config()
        print(f"  模型: YOLOv8n + C2f-EMSCP-EIEM")
        print(f"  创新特性: 边缘信息增强 + 多尺度卷积")
        print(f"  训练轮数: {config['epochs']}")
        print(f"  批次大小: {config['batch']}")
        print(f"  学习率: {config['lr0']}")
        print(f"  预计时间: ~{config['epochs'] * 0.025:.1f}小时")
        
        confirm = input("\n是否开始训练? (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ 训练已取消")
            return
        
        # 3. 开始训练
        results = train_innovation2()
        
        # 4. 分析结果
        if results:
            analyze_results(results)
        
        print("\n" + "=" * 70)
        print("🎉 创新点2训练完成!")
        print("📁 结果保存在: Improve/innovation2-c2f-emscp-eifpn/runs/")
        print("📋 下一步:")
        print("  1. 查看训练图表和边缘增强效果")
        print("  2. 对比Innovation1的性能差异")
        print("  3. 分析边缘信息增强的贡献")
        
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
