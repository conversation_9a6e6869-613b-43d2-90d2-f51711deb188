import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False

# 读取两个模型的训练结果
fdpn_results = pd.read_csv('runs/train/fire-smoke-dataset-yolov8-FDPN/results.csv')
yolov8n_results = pd.read_csv('runs/train/fire-smoke-dataset-yolov8n4/results.csv')

# 清理列名（去除空格）
fdpn_results.columns = fdpn_results.columns.str.strip()
yolov8n_results.columns = yolov8n_results.columns.str.strip()

print("="*80)
print("🔥 FDPN vs YOLOv8n 100轮训练客观对比分析")
print("="*80)

# 最终结果对比
fdpn_final = fdpn_results.iloc[-1]
yolov8n_final = yolov8n_results.iloc[-1]

print("\n📊 最终性能指标对比 (100轮)")
print("-"*70)
metrics = [
    ('mAP50', 'metrics/mAP50(B)'),
    ('mAP50-95', 'metrics/mAP50-95(B)'),
    ('Precision', 'metrics/precision(B)'),
    ('Recall', 'metrics/recall(B)')
]

winner_count = {'FDPN': 0, 'YOLOv8n': 0}

for metric_name, metric_key in metrics:
    fdpn_val = fdpn_final[metric_key]
    yolov8n_val = yolov8n_final[metric_key]
    difference = fdpn_val - yolov8n_val
    percentage_diff = (difference / yolov8n_val) * 100
    
    if fdpn_val > yolov8n_val:
        winner = "🟢 FDPN优胜"
        winner_count['FDPN'] += 1
    else:
        winner = "🔵 YOLOv8n优胜"
        winner_count['YOLOv8n'] += 1
    
    print(f"{metric_name:12} | FDPN: {fdpn_val:.4f} | YOLOv8n: {yolov8n_val:.4f} | 差值: {difference:+.4f} ({percentage_diff:+.1f}%) | {winner}")

print(f"\n🏆 总体表现: YOLOv8n在{winner_count['YOLOv8n']}/4个主要指标上优胜")

# 损失函数对比
print("\n📉 最终损失对比 (越低越好)")
print("-"*70)
loss_metrics = [
    ('Box Loss', 'val/box_loss'),
    ('Class Loss', 'val/cls_loss'),
    ('DFL Loss', 'val/dfl_loss')
]

for loss_name, loss_key in loss_metrics:
    fdpn_loss = fdpn_final[loss_key]
    yolov8n_loss = yolov8n_final[loss_key]
    difference = fdpn_loss - yolov8n_loss
    
    if fdpn_loss < yolov8n_loss:
        winner = "🟢 FDPN更低"
    else:
        winner = "🔵 YOLOv8n更低"
    
    print(f"{loss_name:12} | FDPN: {fdpn_loss:.4f} | YOLOv8n: {yolov8n_loss:.4f} | 差值: {difference:+.4f} | {winner}")

# 训练过程分析
print("\n📈 训练过程深度分析")
print("-"*70)

# 最佳性能点
fdpn_best_map50 = fdpn_results['metrics/mAP50(B)'].max()
fdpn_best_epoch = fdpn_results['metrics/mAP50(B)'].idxmax() + 1

yolov8n_best_map50 = yolov8n_results['metrics/mAP50(B)'].max()
yolov8n_best_epoch = yolov8n_results['metrics/mAP50(B)'].idxmax() + 1

print(f"最佳mAP50对比:")
print(f"  FDPN:    {fdpn_best_map50:.4f} (第{fdpn_best_epoch}轮)")
print(f"  YOLOv8n: {yolov8n_best_map50:.4f} (第{yolov8n_best_epoch}轮)")
print(f"  差值:    {fdpn_best_map50-yolov8n_best_map50:+.4f}")

# 阶段性表现分析
stages = [(20, "早期(1-20轮)"), (50, "中期(1-50轮)"), (100, "全程(1-100轮)")]

print(f"\n🔍 阶段性表现分析:")
for epoch_limit, stage_name in stages:
    fdpn_stage = fdpn_results.head(epoch_limit)
    yolov8n_stage = yolov8n_results.head(epoch_limit)
    
    fdpn_avg = fdpn_stage['metrics/mAP50(B)'].tail(10).mean()
    yolov8n_avg = yolov8n_stage['metrics/mAP50(B)'].tail(10).mean()
    
    print(f"  {stage_name}: FDPN={fdpn_avg:.4f}, YOLOv8n={yolov8n_avg:.4f}, 差值={fdpn_avg-yolov8n_avg:+.4f}")

# 收敛性分析
print(f"\n🎯 收敛性和稳定性分析")
print("-"*50)

# 分析最后10轮的稳定性
fdpn_last10 = fdpn_results.tail(10)['metrics/mAP50(B)']
yolov8n_last10 = yolov8n_results.tail(10)['metrics/mAP50(B)']

fdpn_stability = fdpn_last10.std()
yolov8n_stability = yolov8n_last10.std()

print(f"最后10轮稳定性 (标准差越小越稳定):")
print(f"  FDPN:    {fdpn_stability:.6f}")
print(f"  YOLOv8n: {yolov8n_stability:.6f}")
print(f"  更稳定:  {'FDPN' if fdpn_stability < yolov8n_stability else 'YOLOv8n'}")

# 创建改进的可视化对比图
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('FDPN vs YOLOv8n 训练过程客观对比 (100轮)', fontsize=16, fontweight='bold')

# mAP50对比
axes[0,0].plot(fdpn_results['epoch'], fdpn_results['metrics/mAP50(B)'], 
               label='FDPN', linewidth=2, color='#e74c3c', alpha=0.8)
axes[0,0].plot(yolov8n_results['epoch'], yolov8n_results['metrics/mAP50(B)'], 
               label='YOLOv8n', linewidth=2, color='#3498db', alpha=0.8)
axes[0,0].axhline(y=fdpn_best_map50, color='#e74c3c', linestyle='--', alpha=0.5, label=f'FDPN最佳({fdpn_best_map50:.3f})')
axes[0,0].axhline(y=yolov8n_best_map50, color='#3498db', linestyle='--', alpha=0.5, label=f'YOLOv8n最佳({yolov8n_best_map50:.3f})')
axes[0,0].set_title('mAP50 训练曲线对比', fontweight='bold')
axes[0,0].set_xlabel('Epoch')
axes[0,0].set_ylabel('mAP50')
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)

# mAP50-95对比
axes[0,1].plot(fdpn_results['epoch'], fdpn_results['metrics/mAP50-95(B)'], 
               label='FDPN', linewidth=2, color='#e74c3c', alpha=0.8)
axes[0,1].plot(yolov8n_results['epoch'], yolov8n_results['metrics/mAP50-95(B)'], 
               label='YOLOv8n', linewidth=2, color='#3498db', alpha=0.8)
axes[0,1].set_title('mAP50-95 训练曲线对比', fontweight='bold')
axes[0,1].set_xlabel('Epoch')
axes[0,1].set_ylabel('mAP50-95')
axes[0,1].legend()
axes[0,1].grid(True, alpha=0.3)

# 损失函数对比
axes[1,0].plot(fdpn_results['epoch'], fdpn_results['val/box_loss'], 
               label='FDPN Box Loss', linewidth=2, color='#e74c3c', alpha=0.8)
axes[1,0].plot(yolov8n_results['epoch'], yolov8n_results['val/box_loss'], 
               label='YOLOv8n Box Loss', linewidth=2, color='#3498db', alpha=0.8)
axes[1,0].set_title('验证Box Loss对比', fontweight='bold')
axes[1,0].set_xlabel('Epoch')
axes[1,0].set_ylabel('Box Loss')
axes[1,0].legend()
axes[1,0].grid(True, alpha=0.3)

# 精度vs召回率平衡
axes[1,1].scatter(fdpn_results['metrics/recall(B)'], fdpn_results['metrics/precision(B)'], 
                 c=fdpn_results['epoch'], cmap='Reds', s=30, alpha=0.6, label='FDPN轨迹')
axes[1,1].scatter(yolov8n_results['metrics/recall(B)'], yolov8n_results['metrics/precision(B)'], 
                 c=yolov8n_results['epoch'], cmap='Blues', s=30, alpha=0.6, label='YOLOv8n轨迹')
axes[1,1].set_title('Precision vs Recall 训练轨迹', fontweight='bold')
axes[1,1].set_xlabel('Recall')
axes[1,1].set_ylabel('Precision')
axes[1,1].legend()
axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('fdpn_vs_yolov8n_objective_comparison.png', dpi=300, bbox_inches='tight')
print(f"\n📊 客观对比图表已保存为: fdpn_vs_yolov8n_objective_comparison.png")

# 生成客观分析报告
print("\n" + "="*80)
print("📋 客观分析总结")
print("="*80)

map50_diff = fdpn_final['metrics/mAP50(B)'] - yolov8n_final['metrics/mAP50(B)']
map50_95_diff = fdpn_final['metrics/mAP50-95(B)'] - yolov8n_final['metrics/mAP50-95(B)']

print(f"""
🎯 核心发现:
• 在火灾烟雾检测任务100轮训练后，YOLOv8n基线模型整体表现更优
• YOLOv8n在关键指标mAP50上领先FDPN {abs(map50_diff):.4f} ({abs(map50_diff/yolov8n_final['metrics/mAP50(B)']*100):.1f}%)
• YOLOv8n在mAP50-95上也表现更好，领先{abs(map50_95_diff):.4f}

🔍 详细性能分析:
• mAP50: YOLOv8n {yolov8n_final['metrics/mAP50(B)']:.4f} vs FDPN {fdpn_final['metrics/mAP50(B)']:.4f}
• mAP50-95: YOLOv8n {yolov8n_final['metrics/mAP50-95(B)']:.4f} vs FDPN {fdpn_final['metrics/mAP50-95(B)']:.4f}
• FDPN仅在Precision上略有优势: {fdpn_final['metrics/precision(B)']:.4f} vs {yolov8n_final['metrics/precision(B)']:.4f}

⚡ 训练特性对比:
• YOLOv8n最佳性能: 第{yolov8n_best_epoch}轮达到mAP50 {yolov8n_best_map50:.4f}
• FDPN最佳性能: 第{fdpn_best_epoch}轮达到mAP50 {fdpn_best_map50:.4f}
• YOLOv8n收敛更快，损失函数下降更稳定

🔬 深层分析:
• FDPN的损失函数值普遍较高，特别是分类损失，说明在特征学习上存在困难
• YOLOv8n在多个IoU阈值下都表现更好，说明定位精度更高
• 虽然FDPN在理论上有创新的FocusFeature模块，但在该数据集上未能超越基线

🚀 结论:
在火灾烟雾检测任务上，经典的YOLOv8n架构表现更优。FDPN虽然引入了聚焦扩散
金字塔网络的创新思路，但在实际性能上未能超越基线模型。这提示我们需要:
1. 进一步优化FDPN的超参数设置
2. 分析特征融合模块是否适合该特定任务
3. 考虑数据集特性对不同架构的影响
""")

# 改进建议
print("\n💡 FDPN模型改进建议:")
print("-"*50)
print("1. 调整学习率策略，FDPN可能需要更细致的学习率调度")
print("2. 分析FocusFeature模块的计算开销，可能影响了优化效果")
print("3. 考虑在更大的数据集上验证FDPN的效果")
print("4. 尝试不同的损失函数权重，特别是分类损失部分")
print("5. 分析特征图可视化，了解FDPN特征学习的具体情况") 