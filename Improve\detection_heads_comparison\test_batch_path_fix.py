"""
Test the batch testing path fix in quick_start.py
"""

import os
import sys
from pathlib import Path

def test_batch_script_path_resolution():
    """Test the path resolution for batch testing scripts"""
    print("🔍 Testing batch script path resolution...")
    
    # Get the script directory (same as quick_start.py)
    script_dir = Path(__file__).parent
    print(f"📁 Script directory: {script_dir}")
    
    # Test run_all_tests.py path
    run_all_tests_script = script_dir / 'run_all_tests.py'
    print(f"🔄 Batch testing script: {run_all_tests_script}")
    
    if run_all_tests_script.exists():
        print("✅ run_all_tests.py exists")
        run_all_tests_ok = True
    else:
        print("❌ run_all_tests.py not found")
        run_all_tests_ok = False
    
    # Test compare_results.py path
    compare_results_script = script_dir / 'compare_results.py'
    print(f"📊 Results analysis script: {compare_results_script}")
    
    if compare_results_script.exists():
        print("✅ compare_results.py exists")
        compare_results_ok = True
    else:
        print("❌ compare_results.py not found")
        compare_results_ok = False
    
    return run_all_tests_ok and compare_results_ok

def test_quick_start_path_logic():
    """Test the exact path logic used in quick_start.py"""
    print("\n🧪 Testing quick_start.py path logic...")
    
    # Simulate the exact logic from quick_start.py
    script_dir = Path(__file__).parent  # This simulates Path(__file__).parent in quick_start.py
    
    # Test batch testing script path
    run_all_tests_script = script_dir / 'run_all_tests.py'
    print(f"📋 Batch script path: {run_all_tests_script}")
    print(f"   Exists: {run_all_tests_script.exists()}")
    print(f"   Absolute path: {run_all_tests_script.resolve()}")
    
    # Test results analysis script path
    compare_results_script = script_dir / 'compare_results.py'
    print(f"📋 Analysis script path: {compare_results_script}")
    print(f"   Exists: {compare_results_script.exists()}")
    print(f"   Absolute path: {compare_results_script.resolve()}")
    
    return run_all_tests_script.exists() and compare_results_script.exists()

def test_subprocess_command():
    """Test the subprocess command that would be executed"""
    print("\n🚀 Testing subprocess command construction...")
    
    script_dir = Path(__file__).parent
    run_all_tests_script = script_dir / 'run_all_tests.py'
    
    # This is the command that would be executed
    command = [sys.executable, str(run_all_tests_script)]
    working_dir = str(script_dir)
    
    print(f"📋 Command: {' '.join(command)}")
    print(f"📁 Working directory: {working_dir}")
    print(f"🐍 Python executable: {sys.executable}")
    print(f"📄 Script path: {run_all_tests_script}")
    
    # Verify all components exist
    python_exists = Path(sys.executable).exists()
    script_exists = run_all_tests_script.exists()
    workdir_exists = Path(working_dir).exists()
    
    print(f"✅ Python executable exists: {python_exists}")
    print(f"✅ Script exists: {script_exists}")
    print(f"✅ Working directory exists: {workdir_exists}")
    
    return python_exists and script_exists and workdir_exists

if __name__ == "__main__":
    print("🧪 Testing batch testing path fix")
    print("=" * 50)
    
    # Run all tests
    test1 = test_batch_script_path_resolution()
    test2 = test_quick_start_path_logic()
    test3 = test_subprocess_command()
    
    print(f"\n{'='*50}")
    if test1 and test2 and test3:
        print("🎉 All path resolution tests passed!")
        print("✅ The batch testing fix should work correctly")
        print("\n📋 Next steps:")
        print("1. Run: python quick_start.py")
        print("2. Select: '1. 🔬 运行所有检测头测试'")
        print("3. Confirm with 'y'")
        print("4. The batch testing should now work without path errors")
    else:
        print("❌ Some tests failed - there may still be issues")
        
    print(f"\n🔍 Test Results:")
    print(f"   Script path resolution: {'✅' if test1 else '❌'}")
    print(f"   Quick start logic: {'✅' if test2 else '❌'}")
    print(f"   Subprocess command: {'✅' if test3 else '❌'}")
