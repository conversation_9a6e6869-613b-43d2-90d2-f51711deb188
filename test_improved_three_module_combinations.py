import warnings, os
import torch
import torch.nn as nn
import multiprocessing as mp
warnings.filterwarnings('ignore')
from ultralytics import YOLO

# 改进的三模块组合测试脚本
# 基于分阶段验证结果的针对性改进

if __name__ == '__main__':
    # Windows兼容性设置
    try:
        mp.set_start_method('spawn', force=True)
    except RuntimeError:
        pass
    
    print("=" * 80)
    print("🔧 改进的三模块组合测试")
    print("=" * 80)
    print("📋 基于分阶段验证的发现:")
    print("   ✅ C2f-EIEM: +38.3% - 边缘信息增强非常有效")
    print("   ✅ 简化EMBSFPN: +0.4% - 基本无损害")
    print("   ❌ RSCD检测头: -62.3% - 严重设计缺陷")
    print("   🎯 目标: 保留有效模块，替换失败模块")
    print("=" * 80)
    
    # 测试配置列表
    test_configs = [
        {
            'name': 'EIEM+EMBSFPN+TADDH',
            'config': 'ultralytics/cfg/models/v8/yolov8-EIEM-EMBSFPN-TADDH-fixed.yaml',
            'description': '用TADDH替换失败的RSCD检测头',
            'expected': 'mAP50预期: 0.62-0.65 (+35-42%)'
        },
        {
            'name': 'EIEM+ASF+TADDH', 
            'config': 'ultralytics/cfg/models/v8/yolov8-EIEM-ASF-TADDH.yaml',
            'description': '用ASF替换复杂的EMBSFPN',
            'expected': 'mAP50预期: 0.60-0.63 (+31-38%)'
        },
        {
            'name': 'EIEM+BiFPN+TADDH',
            'config': 'ultralytics/cfg/models/v8/yolov8-EIEM-BiFPN-TADDH.yaml', 
            'description': '最稳定的BiFPN+成熟的TADDH',
            'expected': 'mAP50预期: 0.63-0.66 (+38-44%)'
        }
    ]
    
    results = {}
    
    for i, test_config in enumerate(test_configs, 1):
        print(f"\n🧪 测试 {i}/3: {test_config['name']}")
        print("-" * 60)
        print(f"📝 配置文件: {test_config['config']}")
        print(f"💡 改进思路: {test_config['description']}")
        print(f"🎯 {test_config['expected']}")
        
        try:
            # 1. 模型初始化测试
            print("   📦 加载模型配置...")
            model = YOLO(test_config['config'])
            
            # 2. 加载预训练权重
            print("   🏋️ 加载预训练权重...")
            model.load('yolov8n.pt')
            
            # 3. 模型信息检查
            print("   📊 模型架构验证...")
            model_info = model.info()
            
            # 4. 快速验证训练 (5轮)
            print("   🚀 开始快速验证训练...")
            training_results = model.train(
                # 基础配置
                data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
                epochs=5,  # 快速验证
                patience=10,
                batch=-1,
                imgsz=640,
                
                # Windows兼容设置
                workers=0,
                cache=False,
                
                # 设备和项目
                device="0" if os.environ.get('CUDA_VISIBLE_DEVICES') != '-1' else "cpu",
                project='runs/train',
                name=f'test-{test_config["name"]}-5epochs',
                exist_ok=True,
                
                # 基础训练参数
                pretrained=False,
                amp=True,
                
                # 优化器设置 (基于C2f-EIEM优化经验)
                optimizer="AdamW",
                lr0=0.0008,  # C2f-EIEM适配的学习率
                lrf=0.0001,
                weight_decay=0.0008,
                
                # 学习率调度
                cos_lr=True,
                warmup_epochs=2.0,  # 短验证用较短预热
                
                # 损失权重 (基于成功经验)
                box=7.5,    # BiFPN/TADDH适配
                cls=0.5,    # 降低分类损失权重
                dfl=1.5,
                
                # 数据增强 (适度)
                hsv_h=0.015,
                hsv_s=0.7,
                hsv_v=0.4,
                degrees=10.0,
                translate=0.1,
                scale=0.6,
                mosaic=0.6,
                close_mosaic=3,  # 快速验证用短关闭时间
                
                verbose=True,
                save=True,
                plots=True
            )
            
            # 5. 记录结果
            results[test_config['name']] = {
                'status': '✅ 成功',
                'parameters': model_info.get('parameters', 'N/A'),
                'final_metrics': training_results.results_dict if hasattr(training_results, 'results_dict') else 'N/A'
            }
            
            print(f"   ✅ {test_config['name']} 测试完成!")
            
        except Exception as e:
            results[test_config['name']] = {
                'status': f'❌ 失败: {str(e)}',
                'parameters': 'N/A',
                'final_metrics': 'N/A'
            }
            print(f"   ❌ {test_config['name']} 测试失败: {str(e)}")
            continue
    
    # 6. 结果总结
    print("\n" + "=" * 80)
    print("📊 改进三模块组合测试总结")
    print("=" * 80)
    
    for config_name, result in results.items():
        print(f"\n🔍 {config_name}:")
        print(f"   状态: {result['status']}")
        print(f"   参数量: {result['parameters']}")
        if result['final_metrics'] != 'N/A':
            print(f"   训练结果: 详见 runs/train/test-{config_name}-5epochs/")
    
    # 7. 后续建议
    print(f"\n💡 后续建议:")
    print("1. 对成功的配置进行完整100轮训练")
    print("2. 对比各配置的5轮验证结果")
    print("3. 选择最佳配置进行深度优化")
    print("4. 如果全部失败，考虑进一步简化或使用二模块组合")
    
    print(f"\n📁 结果保存位置: runs/train/test-*-5epochs/")
    print("🎯 重点关注: 训练稳定性、收敛速度、最终mAP")
    
    print("\n" + "=" * 80)
    print("✅ 改进三模块组合测试完成！")
    print("=" * 80) 