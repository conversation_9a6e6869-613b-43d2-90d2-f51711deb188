---
description: Explore the comprehensive reference for ultralytics.utils in the Ultralytics library. Enhance your ML workflow with these utility functions.
keywords: Ultralytics, utils, TQDM, Python, ML, Machine Learning utilities, YOLO, threading, logging, yaml, settings
---

# Reference for `ultralytics/utils/__init__.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/__init__.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/__init__.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/__init__.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.TQDM

<br><br>

## ::: ultralytics.utils.SimpleClass

<br><br>

## ::: ultralytics.utils.IterableSimpleNamespace

<br><br>

## ::: ultralytics.utils.ThreadingLocked

<br><br>

## ::: ultralytics.utils.TryExcept

<br><br>

## ::: ultralytics.utils.Retry

<br><br>

## ::: ultralytics.utils.SettingsManager

<br><br>

## ::: ultralytics.utils.plt_settings

<br><br>

## ::: ultralytics.utils.set_logging

<br><br>

## ::: ultralytics.utils.emojis

<br><br>

## ::: ultralytics.utils.yaml_save

<br><br>

## ::: ultralytics.utils.yaml_load

<br><br>

## ::: ultralytics.utils.yaml_print

<br><br>

## ::: ultralytics.utils.read_device_model

<br><br>

## ::: ultralytics.utils.is_ubuntu

<br><br>

## ::: ultralytics.utils.is_colab

<br><br>

## ::: ultralytics.utils.is_kaggle

<br><br>

## ::: ultralytics.utils.is_jupyter

<br><br>

## ::: ultralytics.utils.is_docker

<br><br>

## ::: ultralytics.utils.is_raspberrypi

<br><br>

## ::: ultralytics.utils.is_jetson

<br><br>

## ::: ultralytics.utils.is_online

<br><br>

## ::: ultralytics.utils.is_pip_package

<br><br>

## ::: ultralytics.utils.is_dir_writeable

<br><br>

## ::: ultralytics.utils.is_pytest_running

<br><br>

## ::: ultralytics.utils.is_github_action_running

<br><br>

## ::: ultralytics.utils.get_git_dir

<br><br>

## ::: ultralytics.utils.is_git_dir

<br><br>

## ::: ultralytics.utils.get_git_origin_url

<br><br>

## ::: ultralytics.utils.get_git_branch

<br><br>

## ::: ultralytics.utils.get_default_args

<br><br>

## ::: ultralytics.utils.get_ubuntu_version

<br><br>

## ::: ultralytics.utils.get_user_config_dir

<br><br>

## ::: ultralytics.utils.colorstr

<br><br>

## ::: ultralytics.utils.remove_colorstr

<br><br>

## ::: ultralytics.utils.threaded

<br><br>

## ::: ultralytics.utils.set_sentry

<br><br>

## ::: ultralytics.utils.deprecation_warn

<br><br>

## ::: ultralytics.utils.clean_url

<br><br>

## ::: ultralytics.utils.url2file

<br><br>
