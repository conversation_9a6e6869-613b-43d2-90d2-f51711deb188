/***************************************************************************************************
 * Copyright (c) 2017-2020, NVIDIA CORPORATION.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 *modification, are permitted provided that the following conditions are met:
 *     * Redistributions of source code must retain the above copyright notice,
 *this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above copyright
 *notice, this list of conditions and the following disclaimer in the
 *documentation and/or other materials provided with the distribution.
 *     * Neither the name of the NVIDIA CORPORATION nor the names of its
 *contributors may be used to endorse or promote products derived from this
 *software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 *AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 *IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 *DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE FOR ANY DIRECT,
 *INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 *OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TOR (INCLUDING
 *NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 *EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 **************************************************************************************************/
/*! \file
    \brief Statically sized array of elements that accommodates all
   CUTLASS-supported numeric types and is safe to use in a union.
*/

#pragma once

#include "cutlass/arch/wmma.h"

#if defined(CUTLASS_ARCH_WMMA_ENABLED)

#include "cutlass/cutlass.h"
#include "cutlass/array.h"

namespace cutlass {

////////////////////////////////////////////////////////////////////////////////////////////////////

/// Wmma array type (WmmaFragmentArray holds elements of of type
/// nvcuda::wmma::fragment)
template <
        /// Element type
        typename T,
        /// Number of elements in the array
        int N>
class WmmaFragmentArray : public Array<T, N, true> {
public:
    /// Efficient clear method (override Array::clear())
    CUTLASS_HOST_DEVICE
    void clear() {
        for (int i = 0; i < Array<T, N, true>::kElements; i++) {
            nvcuda::wmma::fill_fragment((*this)[i],
                                        (typename T::element_type)0);
        }
    }
};

////////////////////////////////////////////////////////////////////////////////////////////////////

}  // namespace cutlass

///////////////////////////////////////////////////////////////////////////////////////////////////

#endif  // if defined(CUTLASS_ARCH_WMMA_ENABLED)
