import torch
import torch.nn as nn
from ultralytics.nn.extra_modules.block import FocusFeature
from ultralytics.nn.modules.conv import Conv

def analyze_focusfeature_detail():
    print("🔍 FocusFeature模块内部复杂度详细分析")
    print("=" * 60)
    
    # 模拟FDPN中FocusFeature的输入配置
    # 基于yolov8-FDPN.yaml中的配置: [[9, 6, 4], 1, FocusFeature, []]
    # 对应P5(1024), P4(512), P3(256)的通道数
    input_channels = [1024, 512, 256]  # [P5, P4, P3]
    e = 0.5  # 默认压缩因子
    kernel_sizes = (5, 7, 9, 11)  # 默认多尺度卷积核
    
    print(f"📊 模块配置:")
    print(f"  输入通道: {input_channels}")
    print(f"  压缩因子: {e}")
    print(f"  卷积核尺寸: {kernel_sizes}")
    
    # 计算中间通道数
    hidc = int(input_channels[1] * e)  # 512 * 0.5 = 256
    print(f"  中间通道数: {hidc}")
    
    print(f"\n🔧 子模块参数量分析:")
    
    # 1. conv1: 上采样+1x1卷积 (P5->P4)
    conv1_params = input_channels[0] * hidc * 1 * 1  # 1024 * 256 * 1 * 1
    print(f"  conv1 (上采样分支): {conv1_params:,} 参数")
    
    # 2. conv2: 1x1卷积 (P4->P4)
    if e != 1:
        conv2_params = input_channels[1] * hidc * 1 * 1  # 512 * 256 * 1 * 1
    else:
        conv2_params = 0
    print(f"  conv2 (中心分支): {conv2_params:,} 参数")
    
    # 3. conv3: ADown下采样 (P3->P4)
    # ADown内部有两个卷积: cv1和cv2
    adown_c = hidc // 2  # 256 // 2 = 128
    cv1_params = (input_channels[2] // 2) * adown_c * 3 * 3  # (256//2) * 128 * 9
    cv2_params = (input_channels[2] // 2) * adown_c * 1 * 1  # (256//2) * 128 * 1
    adown_total = cv1_params + cv2_params
    print(f"  conv3/ADown (下采样分支): {adown_total:,} 参数")
    print(f"    - cv1 (3x3卷积): {cv1_params:,} 参数")
    print(f"    - cv2 (1x1卷积): {cv2_params:,} 参数")
    
    # 4. 多尺度深度卷积
    fusion_channels = hidc * 3  # 256 * 3 = 768
    dw_conv_params = 0
    for k in kernel_sizes:
        # 分组卷积，groups=fusion_channels
        params_per_kernel = fusion_channels * k * k  # 每个卷积核的参数
        dw_conv_params += params_per_kernel
        print(f"    - {k}x{k}深度卷积: {params_per_kernel:,} 参数")
    
    print(f"  多尺度深度卷积总计: {dw_conv_params:,} 参数")
    
    # 5. 点卷积 (pw_conv)
    pw_conv_params = fusion_channels * fusion_channels * 1 * 1  # 768 * 768 * 1
    print(f"  点卷积 (pw_conv): {pw_conv_params:,} 参数")
    
    # 总参数量
    total_params = conv1_params + conv2_params + adown_total + dw_conv_params + pw_conv_params
    print(f"\n📊 总参数量: {total_params:,}")
    
    print(f"\n⚡ 计算复杂度分析 (FLOPs):")
    
    # 假设特征图尺寸为80x80 (P4层级)
    H, W = 80, 80
    
    # conv1: 上采样 + 1x1卷积
    conv1_flops = 2 * H * W * input_channels[0] * hidc  # 2倍因为有上采样
    print(f"  conv1 FLOPs: {conv1_flops:,}")
    
    # conv2: 1x1卷积
    conv2_flops = H * W * input_channels[1] * hidc if e != 1 else 0
    print(f"  conv2 FLOPs: {conv2_flops:,}")
    
    # conv3/ADown: 包含平均池化、最大池化和卷积
    adown_flops = H * W * (input_channels[2] + cv1_params + cv2_params)
    print(f"  ADown FLOPs: {adown_flops:,}")
    
    # 多尺度深度卷积
    dw_flops = 0
    for k in kernel_sizes:
        flops_per_kernel = H * W * fusion_channels * k * k
        dw_flops += flops_per_kernel
    print(f"  多尺度深度卷积 FLOPs: {dw_flops:,}")
    
    # 点卷积
    pw_flops = H * W * fusion_channels * fusion_channels
    print(f"  点卷积 FLOPs: {pw_flops:,}")
    
    total_flops = conv1_flops + conv2_flops + adown_flops + dw_flops + pw_flops
    print(f"\n📈 总计算量: {total_flops:,} FLOPs ({total_flops/1e9:.2f} GFLOPs)")
    
    print(f"\n🎯 模块效率分析:")
    
    # 参数分布
    param_breakdown = {
        'conv1 (上采样)': conv1_params,
        'conv2 (中心)': conv2_params,
        'ADown (下采样)': adown_total,
        '多尺度深度卷积': dw_conv_params,
        '点卷积': pw_conv_params
    }
    
    print(f"  参数分布:")
    for name, params in param_breakdown.items():
        percentage = (params / total_params) * 100
        print(f"    {name}: {percentage:.1f}%")
    
    # 计算分布
    flops_breakdown = {
        'conv1 (上采样)': conv1_flops,
        'conv2 (中心)': conv2_flops,
        'ADown (下采样)': adown_flops,
        '多尺度深度卷积': dw_flops,
        '点卷积': pw_flops
    }
    
    print(f"\n  计算量分布:")
    for name, flops in flops_breakdown.items():
        percentage = (flops / total_flops) * 100
        print(f"    {name}: {percentage:.1f}%")
    
    print(f"\n💡 关键发现:")
    
    # 找出最耗费的组件
    max_param_component = max(param_breakdown.items(), key=lambda x: x[1])
    max_flops_component = max(flops_breakdown.items(), key=lambda x: x[1])
    
    print(f"  参数量最大组件: {max_param_component[0]} ({max_param_component[1]:,} 参数)")
    print(f"  计算量最大组件: {max_flops_component[0]} ({max_flops_component[1]:,} FLOPs)")
    
    # 效率比
    param_flops_ratio = total_params / (total_flops / 1e6)  # 参数数/MFLOPs
    print(f"  参数/计算比: {param_flops_ratio:.2f} 参数/MFLOPs")
    
    print(f"\n🔍 设计优化建议:")
    if pw_conv_params > total_params * 0.5:
        print(f"  ⚠️  点卷积占用了{(pw_conv_params/total_params)*100:.1f}%的参数，可考虑:")
        print(f"     - 使用分组点卷积减少参数")
        print(f"     - 进一步降低中间通道数")
    
    if dw_flops > total_flops * 0.4:
        print(f"  ⚠️  多尺度卷积占用了{(dw_flops/total_flops)*100:.1f}%的计算量，可考虑:")
        print(f"     - 减少卷积核数量")
        print(f"     - 使用可分离卷积")

if __name__ == '__main__':
    analyze_focusfeature_detail() 