#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练结果对比分析脚本
对比 BiFPN 优化模型 vs 基准模型 (YOLOv8n4)
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_analyze_results():
    """加载并分析训练结果"""
    
    # 文件路径（相对于项目根目录）
    baseline_path = "../../../runs/train/fire-smoke-dataset-yolov8n4/results.csv"
    optimized_path = "../../../runs/train/fire-smoke-dataset-yolov8n-bifpn-optimized/results.csv"
    
    # 读取数据并清理列名
    baseline_df = pd.read_csv(baseline_path)
    optimized_df = pd.read_csv(optimized_path)
    
    # 清理列名，移除多余的空格
    baseline_df.columns = baseline_df.columns.str.strip()
    optimized_df.columns = optimized_df.columns.str.strip()
    
    print("=" * 80)
    print("🔥 YOLOv8n-BiFPN 优化模型 vs 基准模型 (YOLOv8n4) 性能对比分析")
    print("=" * 80)
    
    # 基本信息
    print(f"\n📊 基本信息:")
    print(f"基准模型 (YOLOv8n4):")
    print(f"  - 训练轮数: {len(baseline_df)} epochs")
    print(f"  - 数据形状: {baseline_df.shape}")
    
    print(f"\nBiFPN优化模型:")
    print(f"  - 训练轮数: {len(optimized_df)} epochs")
    print(f"  - 数据形状: {optimized_df.shape}")
    
    # 最终性能对比
    print(f"\n🎯 最终性能指标对比 (第100轮):")
    print("-" * 50)
    
    # 获取最后一行数据
    baseline_final = baseline_df.iloc[-1]
    optimized_final = optimized_df.iloc[-1]
    
    # 关键指标对比
    metrics = {
        'mAP50': ('metrics/mAP50(B)', 'mAP@0.5'),
        'mAP50-95': ('metrics/mAP50-95(B)', 'mAP@0.5:0.95'),
        'Precision': ('metrics/precision(B)', 'Precision'),
        'Recall': ('metrics/recall(B)', 'Recall'),
        'Box Loss': ('val/box_loss', 'Validation Box Loss'),
        'Class Loss': ('val/cls_loss', 'Validation Class Loss'),
        'DFL Loss': ('val/dfl_loss', 'Validation DFL Loss')
    }
    
    improvements = {}
    
    for key, (col_name, display_name) in metrics.items():
        if col_name in baseline_final.index and col_name in optimized_final.index:
            baseline_val = baseline_final[col_name]
            optimized_val = optimized_final[col_name]
            
            if 'loss' in col_name.lower():
                # 对于损失函数，越小越好
                improvement = ((baseline_val - optimized_val) / baseline_val) * 100
                change_symbol = "↓" if improvement > 0 else "↑"
            else:
                # 对于准确率指标，越大越好
                improvement = ((optimized_val - baseline_val) / baseline_val) * 100
                change_symbol = "↑" if improvement > 0 else "↓"
            
            improvements[key] = improvement
            
            print(f"{display_name:20}: {baseline_val:.5f} -> {optimized_val:.5f} "
                  f"({change_symbol}{abs(improvement):+.2f}%)")
    
    # 训练过程分析
    print(f"\n📈 训练过程分析:")
    print("-" * 50)
    
    # 收敛性分析
    baseline_convergence = analyze_convergence(baseline_df)
    optimized_convergence = analyze_convergence(optimized_df)
    
    print(f"基准模型收敛性:")
    print(f"  - 最佳 mAP50: {baseline_convergence['best_map50']:.5f} (第{baseline_convergence['best_epoch']}轮)")
    print(f"  - 最终 mAP50: {baseline_convergence['final_map50']:.5f}")
    print(f"  - 稳定性: {baseline_convergence['stability']:.3f}")
    
    print(f"\nBiFPN优化模型收敛性:")
    print(f"  - 最佳 mAP50: {optimized_convergence['best_map50']:.5f} (第{optimized_convergence['best_epoch']}轮)")
    print(f"  - 最终 mAP50: {optimized_convergence['final_map50']:.5f}")
    print(f"  - 稳定性: {optimized_convergence['stability']:.3f}")
    
    # 生成对比图表
    create_comparison_plots(baseline_df, optimized_df)
    
    # 总结
    print(f"\n🏆 总结:")
    print("-" * 50)
    
    # 统计显著改进的指标
    significant_improvements = {k: v for k, v in improvements.items() if abs(v) > 1.0}
    
    if significant_improvements:
        print("显著改进的指标:")
        for metric, improvement in significant_improvements.items():
            status = "提升" if improvement > 0 else "降低"
            print(f"  - {metric}: {status} {abs(improvement):.2f}%")
    
    # 综合评价
    map50_improvement = improvements.get('mAP50', 0)
    map50_95_improvement = improvements.get('mAP50-95', 0)
    
    print(f"\n整体评价:")
    if map50_improvement > 0:
        print(f"✅ BiFPN优化模型在 mAP@0.5 上有 {map50_improvement:.2f}% 的提升")
    else:
        print(f"❌ BiFPN优化模型在 mAP@0.5 上降低了 {abs(map50_improvement):.2f}%")
    
    if map50_95_improvement > 0:
        print(f"✅ BiFPN优化模型在 mAP@0.5:0.95 上有 {map50_95_improvement:.2f}% 的提升")
    else:
        print(f"❌ BiFPN优化模型在 mAP@0.5:0.95 上降低了 {abs(map50_95_improvement):.2f}%")
    
    return baseline_df, optimized_df, improvements

def analyze_convergence(df):
    """分析模型收敛性"""
    map50_col = 'metrics/mAP50(B)'
    
    if map50_col not in df.columns:
        return {'best_map50': 0, 'best_epoch': 0, 'final_map50': 0, 'stability': 0}
    
    map50_values = df[map50_col].values
    best_idx = np.argmax(map50_values)
    best_map50 = map50_values[best_idx]
    best_epoch = best_idx + 1
    final_map50 = map50_values[-1]
    
    # 计算后30轮的标准差作为稳定性指标
    if len(map50_values) >= 30:
        stability = np.std(map50_values[-30:])
    else:
        stability = np.std(map50_values)
    
    return {
        'best_map50': best_map50,
        'best_epoch': best_epoch,
        'final_map50': final_map50,
        'stability': stability
    }

def create_comparison_plots(baseline_df, optimized_df):
    """创建对比图表"""
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('YOLOv8n-BiFPN 优化模型 vs 基准模型 训练过程对比', fontsize=16, fontweight='bold')
    
    # 绘制指标对比
    metrics_to_plot = [
        ('metrics/mAP50(B)', 'mAP@0.5'),
        ('metrics/mAP50-95(B)', 'mAP@0.5:0.95'),
        ('metrics/precision(B)', 'Precision'),
        ('metrics/recall(B)', 'Recall'),
        ('val/box_loss', 'Validation Box Loss'),
        ('val/cls_loss', 'Validation Class Loss')
    ]
    
    for idx, (metric, title) in enumerate(metrics_to_plot):
        ax = axes[idx // 3, idx % 3]
        
        if metric in baseline_df.columns:
            ax.plot(baseline_df['epoch'], baseline_df[metric], 
                   label='基准模型 (YOLOv8n4)', linewidth=2, alpha=0.8)
        
        if metric in optimized_df.columns:
            ax.plot(optimized_df['epoch'], optimized_df[metric], 
                   label='BiFPN优化模型', linewidth=2, alpha=0.8)
        
        ax.set_title(title, fontsize=12, fontweight='bold')
        ax.set_xlabel('Epoch')
        ax.set_ylabel(title)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('training_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 创建损失函数对比图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('训练和验证损失对比', fontsize=16, fontweight='bold')
    
    loss_metrics = [
        ('train/box_loss', 'val/box_loss', 'Box Loss'),
        ('train/cls_loss', 'val/cls_loss', 'Class Loss'),
        ('train/dfl_loss', 'val/dfl_loss', 'DFL Loss')
    ]
    
    for idx, (train_loss, val_loss, title) in enumerate(loss_metrics):
        ax = axes[idx]
        
        # 绘制基准模型
        if train_loss in baseline_df.columns and val_loss in baseline_df.columns:
            ax.plot(baseline_df['epoch'], baseline_df[train_loss], 
                   label='基准模型-训练', linestyle='--', alpha=0.7)
            ax.plot(baseline_df['epoch'], baseline_df[val_loss], 
                   label='基准模型-验证', linewidth=2)
        
        # 绘制优化模型
        if train_loss in optimized_df.columns and val_loss in optimized_df.columns:
            ax.plot(optimized_df['epoch'], optimized_df[train_loss], 
                   label='BiFPN优化-训练', linestyle='--', alpha=0.7)
            ax.plot(optimized_df['epoch'], optimized_df[val_loss], 
                   label='BiFPN优化-验证', linewidth=2)
        
        ax.set_title(title, fontsize=12, fontweight='bold')
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Loss')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('loss_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_performance_summary():
    """创建性能总结表格"""
    
    # 这里可以添加更详细的性能分析
    pass

if __name__ == "__main__":
    try:
        baseline_df, optimized_df, improvements = load_and_analyze_results()
        print(f"\n✅ 分析完成！生成的图表:")
        print(f"  - training_comparison.png: 训练过程对比")
        print(f"  - loss_comparison.png: 损失函数对比")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc() 