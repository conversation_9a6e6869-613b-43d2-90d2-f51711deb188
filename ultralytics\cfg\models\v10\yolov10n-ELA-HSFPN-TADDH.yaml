# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, SCDown, [512, 3, 2]] # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, SCDown, [1024, 3, 2]] # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SP<PERSON><PERSON>, [1024, 5]] # 9
  - [-1, 1, PSA, [1024]] # 10

# YOLOv10.0n head
head:
  - [-1, 1, <PERSON><PERSON>_HSFPN, []] # 11
  - [-1, 1, nn.Conv2d, [512, 1]] # 12
  - [-1, 1, nn.ConvTranspose2d, [512, 3, 2, 1, 1]] # 13

  - [6, 1, E<PERSON>_HSFPN, []] # 14
  - [-1, 1, nn.Conv2d, [512, 1]] # 15
  - [13, 1, ELA_HSFPN, [False]] # 16
  - [[-1, -2], 1, Multiply, []] # 17
  - [[-1, 13], 1, Add, []] # 18
  - [-1, 3, C2f, [512]] # 19 P4/16

  - [13, 1, nn.ConvTranspose2d, [512, 3, 2, 1, 1]] # 20
  - [4, 1, ELA_HSFPN, []] # 21
  - [-1, 1, nn.Conv2d, [512, 1]] # 22
  - [20, 1, ELA_HSFPN, [False]] # 23
  - [[-1, -2], 1, Multiply, []] # 24
  - [[-1, 20], 1, Add, []] # 25
  - [-1, 3, C2f, [512]] # 26 P3/16

  - [[26, 19, 12], 1, v10Detect_TADDH, [nc, 512]] # Detect(P3, P4, P5)
