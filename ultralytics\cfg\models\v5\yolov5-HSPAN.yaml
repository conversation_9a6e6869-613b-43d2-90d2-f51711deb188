# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv5 object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/yolov5

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov5n.yaml' will call yolov5.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]
  s: [0.33, 0.50, 1024]
  m: [0.67, 0.75, 1024]
  l: [1.00, 1.00, 1024]
  x: [1.33, 1.25, 1024]

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, Conv, [64, 6, 2, 2]],  # 0-P1/2
   [-1, 1, Conv, [128, 3, 2]],  # 1-P2/4
   [-1, 3, C3, [128]],
   [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
   [-1, 6, C3, [256]],
   [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
   [-1, 9, C3, [512]],
   [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
   [-1, 3, C3, [1024]],
   [-1, 1, SPPF, [1024, 5]],  # 9
  ]

# YOLOv5 v6.0 head
head:
  [[-1, 1, ChannelAttention_HSFPN, []], # 10
  [-1, 1, nn.Conv2d, [256, 1]], # 11
  [-1, 1, nn.ConvTranspose2d, [256, 3, 2, 1, 1]], # 12

  [6, 1, ChannelAttention_HSFPN, []], # 13
  [-1, 1, nn.Conv2d, [256, 1]], # 14
  [12, 1, ChannelAttention_HSFPN, [4, False]], # 15
  [[-1, -2], 1, Multiply, []], # 16
  [[-1, 12], 1, Add, []], # 17 
  [-1, 3, C2f, [256]], # 18 P4/16

  [12, 1, nn.ConvTranspose2d, [256, 3, 2, 1, 1]], # 19
  [4, 1, ChannelAttention_HSFPN, []], # 20
  [-1, 1, nn.Conv2d, [256, 1]], # 21
  [19, 1, ChannelAttention_HSFPN, [4, False]], # 22
  [[-1, -2], 1, Multiply, []], # 23
  [[-1, 19], 1, Add, []], # 24 
  [-1, 3, C2f, [256]], # 25 P3/8

  [-1, 1, nn.Conv2d, [256, 3, 2, 1]], # 26
  [18, 1, ChannelAttention_HSFPN, []], # 27
  [-1, 1, nn.Conv2d, [256, 1]], # 28
  [26, 1, ChannelAttention_HSFPN, [4, False]], # 29
  [[-1, -2], 1, Multiply, []], # 30
  [[-1, 26], 1, Add, []], # 31
  [-1, 3, C2f, [256]], # 32 P4/16

  [26, 1, nn.Conv2d, [256, 3, 2, 1]], # 33
  [11, 1, ChannelAttention_HSFPN, []], # 34
  [-1, 1, nn.Conv2d, [256, 1]], # 35
  [33, 1, ChannelAttention_HSFPN, [4, False]], # 36
  [[-1, -2], 1, Multiply, []], # 37
  [[-1, 33], 1, Add, []], # 38
  [-1, 3, C2f, [256]], # 39 P5/32

  [[25, 32, 39], 1, Detect, [nc]] # Detect(P3, P4, P5)
  ]