#!/usr/bin/env python3
"""
内存优化的 C2f-EMSCP + TADDH 组合模型训练脚本
解决内存不足问题
"""

from ultralytics import YOLO
import torch
import gc
import os

def optimize_memory():
    """优化内存使用"""
    # 清理缓存
    torch.cuda.empty_cache()
    gc.collect()
    
    # 设置内存优化参数
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'

def main():
    print("=" * 60)
    print("内存优化的 C2f-EMSCP + TADDH 组合模型训练脚本")
    print("🚀 解决内存不足问题")
    print("=" * 60)
    
    # 内存优化
    optimize_memory()
    
    try:
        print("正在加载模型...")
        model = YOLO('ultralytics/cfg/models/v8/yolov8n-C2f-EMSCP-TADDH-safe.yaml')
        
        print("加载预训练权重...")
        model.load('yolov8n.pt')
        
        print("开始内存优化训练...")
        
        # 内存优化的训练参数
        results = model.train(
            # 基本训练参数
            data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            epochs=30,
            patience=60,
            
            # 内存优化参数
            batch=32,  # 减小批次大小
            imgsz=640,
            workers=8,  # 减少工作进程数量
            
            # 数据增强优化（减少内存使用）
            mosaic=0.0,     # 禁用 Mosaic 数据增强
            mixup=0.0,      # 禁用 MixUp
            copy_paste=0.0, # 禁用 Copy-Paste
            
            # 缓存优化
            cache=False,    # 禁用图像缓存
            
            # 训练优化
            save=True,
            save_period=10,
            device=0,
            project='runs/train',
            name='fire-smoke-dataset-memory-optimized',
            exist_ok=True,
            
            # 优化器配置
            optimizer='AdamW',
            lr0=0.001,
            lrf=0.0001,
            momentum=0.937,
            weight_decay=0.0008,
            warmup_epochs=5.0,
            
            # 其他参数
            verbose=True,
            seed=42,
            deterministic=True,
            cos_lr=True,
            close_mosaic=15,
            amp=True,
            plots=True
        )
        
        print("✅ 训练完成！")
        print(f"结果保存在: {results}")
        
        # 清理内存
        del model
        optimize_memory()
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        print("\n💡 建议尝试以下解决方案:")
        print("1. 进一步减小批次大小: batch=8 或 batch=4")
        print("2. 减小图像尺寸: imgsz=512")
        print("3. 减少工作进程: workers=1 或 workers=0")
        print("4. 重启Python进程释放内存")
        
        # 清理内存
        optimize_memory()
        
        # 提供备用配置
        print("\n🔧 自动尝试更保守的配置...")
        try_conservative_training()

def try_conservative_training():
    """尝试更保守的训练配置"""
    try:
        print("使用极度保守的内存配置...")
        optimize_memory()
        
        model = YOLO('ultralytics/cfg/models/v8/yolov8n-C2f-EMSCP-TADDH-safe.yaml')
        model.load('yolov8n.pt')
        
        results = model.train(
            data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            epochs=30,
            batch=4,        # 极小批次
            imgsz=512,      # 较小图像尺寸
            workers=0,      # 单进程
            cache=False,
            mosaic=0.0,
            mixup=0.0,
            copy_paste=0.0,
            device=0,
            project='runs/train',
            name='fire-smoke-dataset-conservative',
            exist_ok=True,
            verbose=True,
            amp=True
        )
        
        print("✅ 保守配置训练成功！")
        return results
        
    except Exception as e:
        print(f"❌ 保守配置也失败了: {e}")
        print("\n💡 最终建议:")
        print("1. 检查可用内存")
        print("2. 关闭其他程序释放内存")
        print("3. 考虑使用更小的数据集进行测试")
        print("4. 尝试在配置更高的机器上训练")

if __name__ == "__main__":
    main() 