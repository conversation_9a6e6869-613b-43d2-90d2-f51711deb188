# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv5 object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/yolov5

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov5n.yaml' will call yolov5.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]
  s: [0.33, 0.50, 1024]
  m: [0.67, 0.75, 1024]
  l: [1.00, 1.00, 1024]
  x: [1.33, 1.25, 1024]

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, H<PERSON>tem, [32, 48]],  # 0-P1/2
   [-1, 6, <PERSON>_<PERSON><PERSON><PERSON>, [48, 128, 3]],  # 1-P2/4
   [-1, 1, D<PERSON>onv, [128, 3, 2, 1, False]],  # 2-P3/8
   [-1, 6, <PERSON><PERSON><PERSON><PERSON><PERSON>, [96, 512, 3]],   # stage 2

   [-1, 1, <PERSON><PERSON><PERSON><PERSON>, [512, 3, 2, 1, False]],  # 4-P3/16
   [-1, 6, <PERSON><PERSON><PERSON><PERSON><PERSON>, [192, 1024, 1, True, <PERSON>alse]],  # cm, c2, k, light, shortcut
   [-1, 6, <PERSON>_<PERSON><PERSON><PERSON>, [192, 1024, 1, True, True]],
   [-1, 6, <PERSON>_H<PERSON><PERSON>, [192, 1024, 1, True, True]],  # stage 3

   [-1, 1, D<PERSON>onv, [1024, 3, 2, 1, False]],  # 8-P4/32
   [-1, 6, <PERSON>_H<PERSON><PERSON>, [384, 2048, 1, True, False]],  # stage 4
   [-1, 1, SPPF, [1024, 5]],  # 10
  ]

# YOLOv5 v6.0 head
head:
  [[-1, 1, Conv, [512, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[-1, 7], 1, Concat, [1]],  # cat backbone P4
   [-1, 3, C3, [512, False]],  # 14

   [-1, 1, Conv, [256, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[-1, 3], 1, Concat, [1]],  # cat backbone P3
   [-1, 3, C3, [256, False]],  # 18 (P3/8-small)

   [-1, 1, Conv, [256, 3, 2]],
   [[-1, 15], 1, Concat, [1]],  # cat head P4
   [-1, 3, C3, [512, False]],  # 21 (P4/16-medium)

   [-1, 1, Conv, [512, 3, 2]],
   [[-1, 11], 1, Concat, [1]],  # cat head P5
   [-1, 3, C3, [1024, False]],  # 24 (P5/32-large)

   [[18, 21, 24], 1, Detect, [nc]],  # Detect(P3, P4, P5)
  ]
