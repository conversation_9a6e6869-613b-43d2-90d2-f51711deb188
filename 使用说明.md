# 基于[ultralytics](https://github.com/ultralytics/ultralytics)的YOLOV8、YOLOV10改进项目 创作人：BiliBili魔傀面具

    ------- From BiliBili 魔傀面具UP -------
    本项目使用的ultralytics版本为8.2.50,在ultralytics/__init__.py中的__version__有标识.
    本项目使用方法与官方一致,我会提供一个我自己常用的方法,或者自己喜欢怎么使用方便就怎么来就行.

    我的实验环境:
    python: 3.10.14
    torch: 2.2.2+cu121
    torchvision: 0.17.2+cu121
    timm: 1.0.7
    mmcv: 2.2.0
    mmengine: 0.10.4

# 本文档最下方有常见疑问解答,这些都是群里高频率的问题！
# 本文档最下方有常见疑问解答,这些都是群里高频率的问题！
# 本文档最下方有常见疑问解答,这些都是群里高频率的问题！
# 本文档最下方有常见疑问解答,这些都是群里高频率的问题！
# 本文档最下方有常见疑问解答,这些都是群里高频率的问题！
# 本文档最下方有常见疑问解答,这些都是群里高频率的问题！

# 如果跑对应的yaml配置文件有报错请先看YOLOV8V10配置文件.md这个文件找到对应配置文件的地方看看有没有标注修改事项！
# 如果跑对应的yaml配置文件有报错请先看YOLOV8V10配置文件.md这个文件找到对应配置文件的地方看看有没有标注修改事项！
# 如果跑对应的yaml配置文件有报错请先看YOLOV8V10配置文件.md这个文件找到对应配置文件的地方看看有没有标注修改事项！
# 如果跑对应的yaml配置文件有报错请先看YOLOV8V10配置文件.md这个文件找到对应配置文件的地方看看有没有标注修改事项！
# 如果跑对应的yaml配置文件有报错请先看YOLOV8V10配置文件.md这个文件找到对应配置文件的地方看看有没有标注修改事项！
# 如果跑对应的yaml配置文件有报错请先看YOLOV8V10配置文件.md这个文件找到对应配置文件的地方看看有没有标注修改事项！

# 常见错误和解决方案在YOLOV8V10配置文件.md！
# 常见错误和解决方案在YOLOV8V10配置文件.md！
# 常见错误和解决方案在YOLOV8V10配置文件.md！
# 常见错误和解决方案在YOLOV8V10配置文件.md！
# 常见错误和解决方案在YOLOV8V10配置文件.md！
# 常见错误和解决方案在YOLOV8V10配置文件.md！

# 实例分割、姿态检测、旋转目标检测怎么用里面的改进视频链接：https://pan.baidu.com/s/1cxIOzMAqGjWgocg9vNDrJQ?pwd=wnvz
# 实例分割、姿态检测、旋转目标检测怎么用里面的改进视频链接：https://pan.baidu.com/s/1cxIOzMAqGjWgocg9vNDrJQ?pwd=wnvz
# 实例分割、姿态检测、旋转目标检测怎么用里面的改进视频链接：https://pan.baidu.com/s/1cxIOzMAqGjWgocg9vNDrJQ?pwd=wnvz
# 实例分割、姿态检测、旋转目标检测怎么用里面的改进视频链接：https://pan.baidu.com/s/1cxIOzMAqGjWgocg9vNDrJQ?pwd=wnvz
# 实例分割、姿态检测、旋转目标检测怎么用里面的改进视频链接：https://pan.baidu.com/s/1cxIOzMAqGjWgocg9vNDrJQ?pwd=wnvz
# 实例分割、姿态检测、旋转目标检测怎么用里面的改进视频链接：https://pan.baidu.com/s/1cxIOzMAqGjWgocg9vNDrJQ?pwd=wnvz
# 实例分割、姿态检测、旋转目标检测怎么用里面的改进视频链接：https://pan.baidu.com/s/1cxIOzMAqGjWgocg9vNDrJQ?pwd=wnvz

# 租用服务器推荐

为了让大家在科研路上一路畅通、降低初期上手难度、并且降低大家租服务器的成本，这边联合多个平台提供一个稳定、快速、便宜的服务器租用平台给大家，经过多次沟通，在我的链接上注册or充值可以给到大家福利如下：

---------------------------------------- 智算云扉 ----------------------------------------
1. 价格非常优惠，几乎全网最低。3090:0.99/h,4090-24GB:最低1.59/h,4090-48GB:3.19/h
2. 使用我的专属优惠码进行充值可以额外获取百分之5的算力点。举个例子:我要充100，本来我只能得100算力点，使用我的优惠码后，可以得到105算力点！下单链接：https://waas.aigate.cc/user/charge?channel=BLBLMGMJ&coupon=DLJGKNBEE1 或者手动填优惠码：DLJGKNBEE1，点击验证即可。优惠码界面在充值入口里面
3. 智算云扉平台上，我已经提供好我自己改进项目的专属镜像、镜像里面会给大家配置好环境、并且相对应需要编译的模型都会给大家配置好、真正实现上传数据集和代码立刻开跑！跑实验也快人一步！镜像分享码:2ukb4hlfPnfT_-DJHFQGm
4. 智算云扉平台上，我为大家提供了一些常用的数据集，并且格式已经转换好，包含COCO2017,VOC2007+2012,CrowdHuman,Visdrone2019,BDD100K.
5. 支持无卡模式开机、支持绑定百度云账号,直接把网盘的内容秒传到云磁盘，省下数据集上传的时间！
6. 可以通过qq搜索以下群号：798692951，添加智算云扉平台交流群，里面有智算云扉官方的客服帮助大家答疑相关平台的问题！
7. B站视频教程：https://www.bilibili.com/video/BV11DXTYiENS/

---------------------------------------- DAModel ----------------------------------------
1. 在DAModel平台上现有的优惠折扣上，额外加上(按需95折、包日97折、包月99折扣优惠)，假如平台租用一台4090按每小时是2.18，假设平台的优惠福利是85折，那么在我的用户下再加上95折，最终价格就是2.18*0.85*0.95=1.76！(优惠目前仅限4090相关服务器)
2. DAModel平台上，我已经提供好我自己改进项目的专属镜像、镜像里面会给大家配置好环境、并且相对应需要编译的模型都会给大家配置好、真正实现上传数据集和代码立刻开跑！跑实验也快人一步！视频参考：https://www.bilibili.com/video/BV1mg2SYGEGF/
3. DAModel平台上，我为大家提供了一些常用的数据集，并且格式已经转换好，包含COCO2017,VOC2007+2012,CrowdHuman,Visdrone2019,BDD100K. 视频参考：https://www.bilibili.com/video/BV1UV5qzuEGf/
4. 谨记，以上福利仅在以下注册链接上进行注册才享有！注册链接：https://damodel.com/register?source=47EC6199
5. 可以通过qq搜索以下群号：728938131，添加DAModel平台交流群，里面有DAModel官方的客服帮助大家答疑相关平台的问题！

# 环境配置

    1. 执行pip uninstall ultralytics把安装在环境里面的ultralytics库卸载干净.<这里需要注意,如果你也在使用yolov8,最好使用anaconda创建一个虚拟环境供本代码使用,避免环境冲突导致一些奇怪的问题>
    2. 卸载完成后同样再执行一次,如果出现WARNING: Skipping ultralytics as it is not installed.证明已经卸载干净.
    3. 如果需要使用官方的CLI运行方式或者多卡运行,需要把ultralytics库安装一下,执行命令:<pip install -e .>,请注意此命令需要在本项目的路径下执行,当然安装后对本代码进行修改依然有效.注意:不需要使用(官方的CLI运行方式、多卡运行),可以选择跳过这步.
    4. 额外需要的包安装命令:
        pip install timm==1.0.7 thop efficientnet_pytorch==0.7.1 einops grad-cam==1.5.4 dill==0.3.8 albumentations==1.4.11 pytorch_wavelets==1.3.0 tidecv PyWavelets opencv-python prettytable -i https://pypi.tuna.tsinghua.edu.cn/simple
        以下主要是使用dyhead必定需要安装的包,如果安装不成功dyhead没办法正常使用!如果执行了还是不成功,可看最下方mmcv安装问题.
        pip install -U openmim -i https://pypi.tuna.tsinghua.edu.cn/simple
        mim install mmengine -i https://pypi.tuna.tsinghua.edu.cn/simple
        mim install "mmcv>=2.0.0" -i https://pypi.tuna.tsinghua.edu.cn/simple
    5. 运行时候如果还缺什么包就请自行安装即可.

    环境安装推荐教程:https://www.bilibili.com/video/BV1VA11YBELB/

    需要编译才能运行的一些模块:
        1. mamba(使用教程请看20240219版本更新说明)
        2. dcnv3(请看百度云视频-DCNV2,DCNV3,DyHeadWithDCNV3相关讲解)
        3. dcnv4(请关闭AMP进行训练,编译教程请看20240116版本更新说明)
        4. smpconv(编译教程请看20240601版本更新说明)
        5. mamba-yolo(编译教程请看20240619版本更新说明)
        6. Kan-Kat(编译教程请看20250207版本更新说明)
            Windows Triton安装参考链接:
            1. https://blog.csdn.net/yyywxk/article/details/144868136
            2. https://github.com/woct0rdho/triton-windows/releases
    
    Windows Triton安装参考链接:
        1. https://blog.csdn.net/yyywxk/article/details/144868136
        2. https://github.com/woct0rdho/triton-windows/releases

    本目录下的test_env.py文件为了验证一些需要编译的或者难安装的(mmcv)是否成功的代码.详细请看以下这期视频:https://pan.baidu.com/s/1sWwvN4UC3blBRVe1twrJAg?pwd=bru5

# 自带的一些文件说明
1. train.py
    训练模型的脚本
2. main_profile.py
    输出模型和模型每一层的参数,计算量的脚本
3. val.py
    使用训练好的模型计算指标的脚本
4. detect.py
    推理的脚本
5. track.py
    跟踪推理的脚本
6. test_yaml.py
    用来测试所有yaml是否能正常运行的脚本
7. heatmap.py  
    生成热力图的脚本
8. get_FPS.py
    计算模型储存大小、模型推理时间、FPS的脚本
9. get_COCO_metrice.py
    计算COCO指标的脚本
10. plot_result.py
    绘制曲线对比图的脚本，可看百度云视频-plot_result.py使用教程
11. transform_PGI.py
    去掉PGI模块.
12. export.py
    导出onnx脚本.[视频链接](https://www.bilibili.com/video/BV1CK421e7Y3/)
13. get_model_erf.py
    绘制模型的有效感受野.[视频链接](https://www.bilibili.com/video/BV1Gx4y1v7ZZ/)
14. test_env.py
    验证一些需要编译的或者难安装的(mmcv)是否成功的代码.[百度云链接](https://pan.baidu.com/s/1sWwvN4UC3blBRVe1twrJAg?pwd=bru5)
15. get_all_yaml_param_and_flops.py
    计算所有yaml的计算量并排序.[百度云链接](https://pan.baidu.com/s/1ZDzglU7EIzzfaUDhAhagBA?pwd=kg8k)

# 模型配置文件
模型配置文件都在ultralytics/cfg/models/v8中.
yolov8有五种大小的模型,以下模型参数量和计算量均为类别80且重参数化后计算.

    YOLOv8n summary: 225 layers,  3157200 parameters,  3157184 gradients,   8.9 GFLOPs
    YOLOv8s summary: 225 layers, 11166560 parameters, 11166544 gradients,  28.8 GFLOPs
    YOLOv8m summary: 295 layers, 25902640 parameters, 25902624 gradients,  79.3 GFLOPs
    YOLOv8l summary: 365 layers, 43691520 parameters, 43691504 gradients, 165.7 GFLOPs
    YOLOv8x summary: 365 layers, 68229648 parameters, 68229632 gradients, 258.5 GFLOPs

模型配置文件都在ultralytics/cfg/models/v10中.
yolov10预训练权重下载链接：https://github.com/THU-MIG/yolov10/releases
yolov10有六种大小的模型,以下模型参数量和计算量均为类别80且重参数化后计算.

    YOLOv10n summary: 229 layers,  2299264 parameters,  2286448 gradients,   6.7 GFLOPs
    YOLOv10s summary: 237 layers,  7248960 parameters,  7197744 gradients,  21.6 GFLOPs
    YOLOv10m summary: 313 layers, 15359488 parameters, 15359472 gradients,  59.1 GFLOPs
    YOLOv10b summary: 327 layers, 19065792 parameters, 19065776 gradients,  92.0 GFLOPs
    YOLOv10l summary: 405 layers, 24371008 parameters, 24370992 gradients, 120.3 GFLOPs
    YOLOv10x summary: 447 layers, 29473568 parameters, 29473552 gradients, 160.4 GFLOPs

# 常见疑问
1. Fuse指的是什么？

    Fuse是指模型的一些模块进行融合,最常见的就是conv和bn层进行融合,在训练的时候模型是存在conv和bn的,但在推理的过程中,模型在初始化的时候会进行模型fuse,把其中的conv和bn进行融合,通过一些数学转换把bn层融合到conv里面,还有一些例如DBB,RepVGG等等模块支持融合的,这些在fuse阶段都会进行融合,融合后可以一般都可以得到比融合前更快的推理速度,而且基本不影响精度.

2. FPS如何计算？

    1. 在运行val.py后最后会出来Speed: 0.1ms preprocess, 5.4ms inference, 0.0ms loss, 0.4ms postprocess per image这行输出,这行输出就代表了每张图的前处理,推理,loss,后处理的时间,当然在val.py过程中是不需要计算loss的,所以为0,FPS最严谨来说就是1000(1s)/(preprocess+inference+postprocess),没那么严谨的话就是只除以inference的时间,还有一个问题就是batchsize应该设置为多少,其实这行输出就已经是每张图的时间了,但是batchsize还是会对这个时间有所影响,主要是关于并行处理的问题,GPU中可以一次处理多个batch的数据,也可以只处理一个数据,但是处理多batch的数据比处理一个数据的时候整体速度要快,举个例子,比如我有1000张图,我分别设置batchsize为32和batchsize为1,整体运行的时间百分之99都是batchsize为32的快,因此这就导致不同batch输出的时间不同,至于该设置多少来计算FPS,貌似众说纷纭,所以这里我也不好给意见.  
    附上yolov5作者对于FPS和Batch的一个实验链接: https://github.com/ultralytics/yolov5/discussions/6649
    2. 项目中的get_FPS.py是只算推理时间.
    3. batch问题,比如你设置为16,那所有对比的模型都在同一个batch来计算即可.
    4. 小模型尽量要大bs(16,32)测.

3. 怎么像yolov5那样输出每一层的参数,计算量？

    使用main_profile.py,选择自己的配置文件路径即可

4. 怎么找到对应模块的说明视频?

    在2024.7.27版本后在YOLOV8V10配置文件.md文件上已经标注,部分复杂度低、较简单的没有视频链接.[更新公告](https://blog.csdn.net/qq_37706472/article/details/136178142)

5. 保存的模型存储大小问题.

    在训练图中看保存的模型大小是会比训练结束后的偏大,因为其会保存一些过程中的一些其他信息,但这些不会影响原本模型的参数量和计算量,等训练结束后,其会自己读取清除额外的信息.

6. YOLOV8怎么指定使用哪一种大小的模型呢？

    假设我选择的配置文件是yolov8.yaml,我想选择m大小的模型,则train.py中的指定为ultralytics/cfg/models/v8/yolov8m.yaml即可,同理,如果我想指定s大小的模型,则指定为ultralytics/cfg/models/v8/yolov8s.yaml即可,如果直接设置为ultralytics/cfg/models/v8/yolov8.yaml,则默认使用n大小模型,又或者我需要使用ultralytics/cfg/models/v8/yolov8-bifpn.yaml,我需要设定为s模型,则应该为ultralytics/cfg/models/v8/yolov8s-bifpn.yaml.

7. 热力图使用脚本相关问题.

    1. 需要安装grad-cam==1.5.4
    2. 使用示例：https://www.bilibili.com/video/BV1T2N6eaEFD/  如果更换了主干还需看：https://www.bilibili.com/video/BV1F6421V77v/
    3. 进度条不满是正常现象,只要进度条不是0,都可以进行出图.

8. 怎么判断模型收敛了？模型会不会过拟合？

    可以看下这期[视频](https://www.bilibili.com/video/BV11S421d76P/)
    1. 主要看训练结束后的result.png中的精度曲线,精度曲线没有上升的趋势就可以了.
    2. 很多场景的数据下在曲线上都会呈现像过拟合的趋势,但是代码中已经会自动保存best.pt,用best.pt可以避免训练后期过拟合导致的精度下降等等影响,简单来说就是只需要用best.pt即可,不需要理会过拟合的问题.

9. 曲线震荡问题.

    这类问题都不好解决,如果基础模型就震荡很厉害,基本都是跟数据集有关系,如果改进后的模型后出现,基本都是改进模型不合适的问题.

10. 绘制结构图问题.

    可看以下这两个视频:
    1. https://www.bilibili.com/video/BV1X94y1K76Z/
    2. https://www.bilibili.com/video/BV1WA4m1V7nQ/

11. mmcv安装问题.

    可以看官方文档：https://mmcv.readthedocs.io/en/latest/get_started/installation.html#install-with-pip
 
12. 预训练权重相关问题.

    可以看这个视频：https://www.bilibili.com/video/BV1Q1421Q7Zw/
    不载入预训练权重的话，只需要在train.py中的model.load注释即可！

13. 绘制结构图教程.

    1. [什么？你说你不会画模型结构图？行吧，那你进来看看吧，手把手教你画YAML结构图！](https://www.bilibili.com/video/BV1X94y1K76Z/)
    2. [什么？你说你更换主干后看不懂配置文件也不懂画结构图？那你快点进来看看了！](https://www.bilibili.com/video/BV1WA4m1V7nQ/)
    3. [从简到难手把手教你画Pytorch模块内的结构图！](https://www.bilibili.com/video/BV1dC411p7H7/)

14. 配置文件整合问题.

    1. [不会把多个改进整合到一个yaml配置文件里面？那来看看这个吧！从简到难手把手带你整合三个yaml](https://www.bilibili.com/video/BV15H4y1Y7a2/)
    2. [细谈目标检测中的小目标检测头和大目标检测检测头，并教懂你怎么加微小目标、极大目标检测头！](https://www.bilibili.com/video/BV1jkDWYFEwx/)
    3. [不会看YOLO的模型yaml配置文件？那你还怎么整合多个配置文件！](https://www.bilibili.com/video/BV1oiBRYnEEw/)
    4. [不会把多个创新点整合到一个yaml配置文件里面？那来看看这个吧！手把手来你整合创新点！](https://www.bilibili.com/video/BV1DUBRYGE3b/)

15. 训练结果可重现的问题.

    1. torch版本需要大于等于2.0.0才会开启确定性训练算法.
    2. 训练中不能开启cache参数.
    3. 满足以上条件外,还要看模型中是否有不支持确定性训练算法的操作,pytorch官网也标注,即使开启确定性训练算法也不是百分百能保证训练结果的可重现性.

16. 参数详解.

    1. [训练参数官方文档地址](https://docs.ultralytics.com/modes/train/#resuming-interrupted-trainings:~:text=a%20training%20run.-,Train%20Settings,-The%20training%20settings)
    2. [验证参数官方文档地址](https://docs.ultralytics.com/modes/val/#usage-examples:~:text=of%20each%20category-,Arguments%20for%20YOLO%20Model%20Validation,-When%20validating%20YOLO)
    3. [推理参数官方文档地址](https://docs.ultralytics.com/modes/predict/#inference-sources:~:text=of%20Results%20objects-,Inference%20Arguments,-model.predict())
    4. [导出参数官方文档地址](https://docs.ultralytics.com/modes/export/#usage-examples)

17. Freezing layer 'model.22.dfl.conv.weight'

    这个是正常的，这一层就是不需要训练。

18. 为什么程序开始的时候会下载yolov8n.pt.

    这个是用于AMP混合精度训练测试用的，不是预训练权重，如果下载慢，可以自行下载后放到运行目录下，它检测到有这个文件就不会下载。

19. 为什么训练过程中显存不断在变化？有时大有时小？

    因为yolov8中的标签分配策略的问题，正常现象。

20. 如何关闭早停？

    train.py中设置patience为0.

21. 设置了epochs为300，但是发现还没有收敛，可以在300次的基础上再训练100次吗？

    不行，训练了300次后再训练100次跟一次性训练400次的结果很大概率不一样，因为它们的学习率匹配不上。

22. 我的验证集精度0.6，但是测试集精度只有0.5，这是什么问题？

    没问题，一般是由于测试集与验证集差异较大导致，如果是私有数据集可以考虑重新合并随机划分，如果是公开数据集就不用管，例如visdrone2019，验证集也跟测试集相差10个点。

23. 预测的时候在同一个目标上出现了两个框，怎么解决？

    在detect.py中设置agnostic_nms=True，原始的nms是基于每个类里面进行的，设置agnostic_nms=True就是所有目标下进行，不会细分类别，可以解决这个问题。

24. 能不能不要分测试集？

    除非是公开数据集没有测试集，否则不建议，如果到时候审稿人问到为什么你没有测试集，你没有一个合理的理由可以回复，最坏的结果是实验重做。

25. 如何可视化中间层的特征图？

    运行detect.py中并设置visualize=True.

26. 为什么yolov8-p2.yaml比yolov8.yaml参数量要小，但是计算量变大?

    正常现象,可看[github-issue链接](https://github.com/ultralytics/ultralytics/issues/7502).

27. 像项目自带的v3、v5配置文件能直接用来做模型的对比实验吗？

    不可以，因为里面用的都是v8的anchorfree的头，不是原版的模型。
    除了v8、v10可以用，其他的都需要去官方的代码上跑.

28. 怎么关闭混合精度训练？

    train.py中设置参数amp=False.

29. 训练时候输出: ERROR:albumentations.check_version:Error fetching version info 怎么办？

    不用理会，应该是albumentations在检测最新的版本号时网络连接失败.

30. 怎么计算small、middle、large和COCO指标？

    python dataset/yolo2coco.py --image_path dataset/images/test --label_path dataset/labels/test  
    python get_COCO_metrice.py --pred_json runs/val/exp/predictions.json --anno_json data.json  
    视频教程请看百度云视频-计算COCO指标教程.

31. 如何绘制曲线对比图?

    本项目内的plot_result.py使用教程.
    各种不同类型的模型曲线对比图和精度对比图也可以看这期视频：https://www.bilibili.com/video/BV1yf421X7t5/

32. 如何替换主干？

    可以看项目视频-添加我的github仓库上别的主干说明.

33. 计算量、参数量以哪个脚本输出的为准？

    以val.py输出的结果为准，val.py输出的是重参数化后的结果，论文中以这个结果为准.

# YOLOV8源码常见疑问解答小课堂
1. [关于配置文件中Optimizer参数为auto的时候，究竟Optimizer会怎么选用呢？](https://www.bilibili.com/video/BV1K34y1w7cZ/)
2. [best.pt究竟是根据什么指标来保存的?](https://www.bilibili.com/video/BV1jN411M7MA/)
3. [数据增强在yolov8中的应用](https://www.bilibili.com/video/BV1aQ4y1g7ah/)
4. [如何添加FPS计算代码和FPS的相关的一些疑问](https://www.bilibili.com/video/BV1Sw411g7DD/)
5. [预测框粗细颜色修改与精度小数位修改](https://www.bilibili.com/video/BV12K421a7rH/)
6. [导出改进/剪枝的onnx模型和讲解onnx-opset和onnxsim的作用](https://www.bilibili.com/video/BV1CK421e7Y3/)
7. [YOLOV8模型详细讲解(包含该如何改进YOLOV8)(刚入门小白，需要改进YOLOV8的同学必看！)](https://www.bilibili.com/video/BV1Ms421u7VH/)
8. [学习率变化问题](https://www.bilibili.com/video/BV1frnferEL1/)

# 一些非常推荐小白看的视频链接
1. [YOLOV8模型详细讲解(包含该如何改进YOLOV8)(刚入门小白，需要改进YOLOV8的同学必看！)](https://www.bilibili.com/video/BV1Ms421u7VH/)
2. [提升多少才能发paper？轻量化需要看什么指标？需要轻量化到什么程度才能发paper？这期给大家一一解答！](https://www.bilibili.com/video/BV1QZ421M7gu/)
3. [深度学习实验部分常见疑问解答！(小白刚入门必看！少走弯路！少自我内耗！)](https://www.bilibili.com/video/BV1Bz421B7pC/)
    ```
    1. 如何衡量自己的所做的工作量够不够？
    2. 为什么别人的论文说这个模块对xxx有作用，但是我自己用的时候还掉点了？
    3. 提升是和什么模型相比呢 比如和yolov8这种基础模型比还是和别人提出的目前最好的模型比
    4. 对比不同的模型的时候，输入尺寸，学习率，学习次数这些是否需要一致？
    ```
4. [深度学习实验部分常见疑问解答二！(小白刚入门必看！少走弯路！少自我内耗！)](https://www.bilibili.com/video/BV1ZM4m1m785/)
    ```
    1. 为什么我用yolov8自带的coco8、coco128训练出来的效果很差？
    2. 我的数据集很大，机器跑得慢，我是否可以用数据集的百分之10的数据去测试这个改进点是否有效？有效再跑整个数据集？
    ```
5. [深度学习实验部分常见疑问解答三！(怎么判断模型是否收敛？模型过拟合怎么办？)](https://www.bilibili.com/video/BV11S421d76P/)
6. [YOLO系列模型训练结果详细解答！(训练过程的一些疑问，该放哪个文件运行出来的结果、参数量计算量在哪里看..等等问题)](https://www.bilibili.com/video/BV11b421J7Vx/)
7. [细谈目标检测中的小目标检测头和大目标检测检测头，并教懂你怎么加微小目标、极大目标检测头！](https://www.bilibili.com/video/BV1jkDWYFEwx/)
8. [深度学习炼丹必备必看必须知道的小技巧！](https://www.bilibili.com/video/BV1q3SZYsExc/)