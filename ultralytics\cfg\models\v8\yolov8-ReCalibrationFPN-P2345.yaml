# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv8 object detection model with P2-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n.yaml' will call yolov8.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]
  s: [0.33, 0.50, 1024]
  m: [0.67, 0.75, 768]
  l: [1.00, 1.00, 512]
  x: [1.00, 1.25, 512]

# YOLOv8.0 backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]]  # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]]  # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]]  # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, Conv, [512, 3, 2]]  # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, Conv, [1024, 3, 2]]  # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SPPF, [1024, 5]]  # 9

# YOLOv8.0-p2 head
head:
  - [[-1, 6], 1, SBA, []] # 10 (P4/16)
  - [-1, 3, C2f, [512]] # 11

  - [[-1, 4], 1, SBA, []] # 12 (P3/8)
  - [-1, 3, C2f, [256]] # 13

  - [[-1, 2], 1, SBA, []] # 14 (P2/4)
  - [-1, 3, C2f, [128]] # 15

  - [[-1, 13], 1, SBA, []] # 16 (P3/8)
  - [-1, 3, C2f, [256]] # 17

  - [[-1, 11], 1, SBA, []] # 18 (P4/16)
  - [-1, 3, C2f, [512]] # 19

  - [[-1, 9], 1, SBA, []] # 10 (P5/32)
  - [-1, 3, C2f, [1024]] # 21

  - [[15, 17, 19, 21], 1, Detect, [nc]]  # Detect(P2, P3, P4, P5)