
# `train.py` 训练配置文档

本文档详细记录了 `E:\cursor\yolo3\ultralytics-main\train.py` 文件中用于模型训练的各项参数配置。

---

### **1. 模型与数据集 (Model & Dataset)**

- **模型配置文件 (Model Configuration File):**
  - `ultralytics/cfg/models/v8/yolov8n-bifpn.yaml`
  - 这意味着训练将使用一个基于 `YOLOv8n` 骨干，并集成了 `BiFPN` (双向特征金字塔网络) 的模型结构。

- **数据集配置文件 (Dataset Configuration File):**
  - `ultralytics/cfg/datasets/fire-smoke-dataset.yaml`
  - 训练将使用此文件定义的火焰和烟雾数据集。

---

### **2. 训练核心参数 (Core Training Parameters)**

这是传递给 `model.train()` 方法的主要参数，已针对火焰和烟雾双分类任务进行了优化。

#### **2.1. 基础训练设置 (Basic Training Settings)**

- `epochs`: **10**
  - 训练的总轮数。当前设置为一个较小的值，适合快速测试。对于正式训练，建议增加到50-100轮。
- `patience`: **40**
  - 在验证集上，如果连续40个epoch性能没有提升，则提前停止训练。较高的值有助于模型在双分类任务中充分学习。
- `batch`: **-1** (自动)
  - 批处理大小。设置为-1表示由Ultralytics框架根据硬件显存自动选择最优的batch size。
- `imgsz`: **640**
  - 输入网络的图像尺寸统一调整为 640x640。
- `pretrained`: **False**
  - 从零开始训练，不加载任何预训练权重。
- `single_cls`: **False**
  - 表明这是一个多类别（或双类别）检测任务，而不是单类别。
- `close_mosaic`: **5**
  - 在最后5个epoch关闭马赛克数据增强，这有助于模型学习完整的物体特征，特别是对于形状多变的火焰。
- `amp`: **False**
  - 关闭自动混合精度（AMP）训练。这可以增加训练的稳定性，尤其是在使用某些自定义或对FP16敏感的模块时。

#### **2.2. 优化器与学习率 (Optimizer & Learning Rate)**

- `optimizer`: **"AdamW"**
  - 使用 AdamW 优化器，它通常比 SGD 更适合处理复杂特征（如火焰）的收敛。
- `lr0`: **0.0015**
  - 初始学习率。
- `lrf`: **0.001**
  - 最终学习率是初始学习率的 `lrf` 倍 (`lr0 * lrf`)。
- `cos_lr`: **True**
  - 启用余弦退火学习率调度策略，使学习率随训练进程平滑下降。
- `warmup_epochs`: **2.0**
  - 在前2个epoch进行学习率预热，帮助模型在训练初期稳定收敛。

#### **2.3. 损失权重 (Loss Weights)**

- `box`: **8.0**
  - 边界框回归损失的权重。较高的值表明更重视目标定位的准确性。
- `cls`: **1.0**
  - 分类损失的权重。针对双分类任务，此权重被提高，以帮助模型更好地区分火焰和烟雾。
- `dfl`: **2.0**
  - 分布焦点损失（Distribution Focal Loss）的权重，用于改善边界框的定位精度。

#### **2.4. 数据增强 (Data Augmentation)**

- `hsv_h`: **0.025** (色调)
- `hsv_s`: **0.8** (饱和度)
- `hsv_v`: **0.6** (亮度)
  - 针对火焰和烟雾在颜色、饱和度上的差异进行了特定增强。
- `degrees`: **15.0** (旋转)
- `translate`: **0.2** (平移)
- `scale`: **0.8** (缩放)
  - 增强模型对火焰和烟雾在不同姿态、位置和大小下的鲁棒性。
- `mosaic`: **0.8**
  - 马赛克数据增强的概率。适当降低以更好地保留火焰的完整形状。
- `mixup`: **0.0** (关闭)
- `copy_paste`: **0.0** (关闭)
  - 关闭了 Mixup 和 Copy-Paste 增强，因为它们可能会干扰火焰和烟雾的自然特征。

#### **2.5. 环境与保存 (Environment & Saving)**

- `device`: **"0"**
  - 使用索引为0的GPU进行训练。如果检测到`CUDA_VISIBLE_DEVICES`为`-1`，则使用CPU。
- `workers`: **8**
  - 数据加载时使用的线程数。
- `project`: **'runs/train'**
  - 所有训练结果将保存在此目录下。
- `name`: **'fire-smoke-dataset-yolov8n-bifpn'**
  - 本次训练的特定实验名称。
- `exist_ok`: **True**
  - 如果实验目录已存在，则覆盖它。
- `seed`: **42**
- `deterministic`: **True**
  - 设置随机种子并启用确定性训练，以确保实验结果的可复现性。
- `cache`: **True**
  - 缓存数据集到内存或本地磁盘，以加快后续训练的数据加载速度。

--- 