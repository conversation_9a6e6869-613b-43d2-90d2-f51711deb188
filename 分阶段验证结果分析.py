import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams
import seaborn as sns

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False

def analyze_staged_validation():
    """
    分析分阶段验证的训练结果
    """
    print("="*80)
    print("🔬 分阶段验证结果分析报告")
    print("="*80)
    
    # 从训练输出中提取的关键数据
    stage_results = {
        'YOLOv8n (基线)': {
            'epochs': 5,
            'final_mAP50': 0.4571,
            'final_mAP50_95': 0.2120,
            'final_precision': 0.5371,
            'final_recall': 0.4389,
            'parameters': '~3M',
            'description': '标准YOLOv8n基线模型'
        },
        'C2f-EIEM (阶段1)': {
            'epochs': 10,
            'final_mAP50': 0.632,
            'final_mAP50_95': 0.34,
            'final_precision': 0.685,
            'final_recall': 0.564,
            'parameters': '2.9M',
            'description': '仅使用C2f-EIEM边缘信息增强'
        },
        'EIEM+简化EMBSFPN (阶段2)': {
            'epochs': 15,
            'final_mAP50': 0.634,
            'final_mAP50_95': 0.336,
            'final_precision': 0.682,
            'final_recall': 0.573,
            'parameters': '1.8M',
            'description': 'C2f-EIEM + 简化EMBSFPN'
        },
        'EIEM+EMBSFPN+RSCD (阶段3)': {
            'epochs': 50,
            'final_mAP50': 0.238,
            'final_mAP50_95': 0.0834,
            'final_precision': 0.352,
            'final_recall': 0.270,
            'parameters': '1.7M',
            'description': '完整三模块组合'
        }
    }
    
    print("\n📊 各阶段最终性能对比")
    print("-"*80)
    print(f"{'模型':<25} {'mAP50':>8} {'mAP50-95':>10} {'Precision':>10} {'Recall':>8} {'参数量':>8}")
    print("-"*80)
    
    baseline_map50 = stage_results['YOLOv8n (基线)']['final_mAP50']
    
    for stage, data in stage_results.items():
        map50 = data['final_mAP50']
        map50_95 = data['final_mAP50_95']
        precision = data['final_precision']
        recall = data['final_recall']
        params = data['parameters']
        
        improvement = ((map50 - baseline_map50) / baseline_map50) * 100 if stage != 'YOLOv8n (基线)' else 0.0
        status = "📈" if improvement > 0 else "📉" if improvement < 0 else "➖"
        
        print(f"{stage:<25} {map50:>8.3f} {map50_95:>10.3f} {precision:>10.3f} {recall:>8.3f} {params:>8}")
        if stage != 'YOLOv8n (基线)':
            print(f"{'vs基线变化:':<25} {status} {improvement:+6.1f}%")
    
    print("\n🔍 关键发现")
    print("-"*60)
    
    # 1. C2f-EIEM模块效果分析
    eiem_improvement = ((stage_results['C2f-EIEM (阶段1)']['final_mAP50'] - baseline_map50) / baseline_map50) * 100
    print(f"1. **C2f-EIEM模块单独效果**: {eiem_improvement:+.1f}%")
    
    if eiem_improvement > 10:
        print("   ✅ 边缘信息增强模块非常有效！显著提升检测精度")
    elif eiem_improvement > 5:
        print("   ✅ 边缘信息增强模块有效，适度提升性能")
    elif eiem_improvement > 0:
        print("   ⚠️ 边缘信息增强模块有轻微提升，但效果有限")
    else:
        print("   ❌ 边缘信息增强模块可能存在问题")
    
    # 2. 简化EMBSFPN效果分析
    embsfpn_map50 = stage_results['EIEM+简化EMBSFPN (阶段2)']['final_mAP50']
    eiem_map50 = stage_results['C2f-EIEM (阶段1)']['final_mAP50']
    embsfpn_effect = ((embsfpn_map50 - eiem_map50) / eiem_map50) * 100
    
    print(f"\n2. **简化EMBSFPN效果**: {embsfpn_effect:+.1f}%")
    
    if abs(embsfpn_effect) < 1:
        print("   ✅ 简化EMBSFPN基本持平，架构可行")
    elif embsfpn_effect > 0:
        print("   ✅ 简化EMBSFPN有提升效果")
    else:
        print("   ⚠️ 简化EMBSFPN可能引入负面影响")
    
    # 3. 完整模块组合分析
    full_map50 = stage_results['EIEM+EMBSFPN+RSCD (阶段3)']['final_mAP50']
    full_vs_baseline = ((full_map50 - baseline_map50) / baseline_map50) * 100
    full_vs_eiem = ((full_map50 - eiem_map50) / eiem_map50) * 100
    
    print(f"\n3. **完整模块组合效果**: {full_vs_baseline:+.1f}% (vs基线)")
    print(f"   相比C2f-EIEM: {full_vs_eiem:+.1f}%")
    
    if full_vs_baseline < -30:
        print("   ❌ 完整组合严重失败，三模块不兼容")
    elif full_vs_baseline < -10:
        print("   ❌ 完整组合效果差，需要重新设计")
    elif full_vs_baseline < 0:
        print("   ⚠️ 完整组合有问题，需要优化")
    else:
        print("   ✅ 完整组合基本可行")
    
    # 4. 详细问题诊断
    print(f"\n🔧 问题诊断")
    print("-"*40)
    
    # 参数量分析
    print("参数量变化:")
    print(f"  - 基线YOLOv8n: ~3M参数")
    print(f"  - C2f-EIEM: 2.9M参数 (略少)")
    print(f"  - 简化EMBSFPN: 1.8M参数 (大幅减少)")
    print(f"  - 完整模块: 1.7M参数 (最少)")
    
    # 性能分析
    print("\n性能表现分析:")
    if eiem_improvement > 20:
        print("  ✅ C2f-EIEM模块是明显的性能提升点")
        print("  📊 边缘信息增强确实有效")
    
    if embsfpn_effect > -5:
        print("  ✅ 简化EMBSFPN没有明显损害性能")
    
    if full_vs_eiem < -50:
        print("  ❌ RSCD检测头或完整组合存在严重问题")
        print("  🔧 建议: 使用C2f-EIEM + 标准检测头")
    
    # 5. 训练特性分析
    print(f"\n📈 训练特性分析")
    print("-"*40)
    
    print("收敛性:")
    print(f"  - C2f-EIEM: 10轮达到{stage_results['C2f-EIEM (阶段1)']['final_mAP50']:.3f}")
    print(f"  - 简化EMBSFPN: 15轮达到{stage_results['EIEM+简化EMBSFPN (阶段2)']['final_mAP50']:.3f}")
    print(f"  - 完整模块: 50轮仅达到{stage_results['EIEM+EMBSFPN+RSCD (阶段3)']['final_mAP50']:.3f}")
    
    print("\n训练稳定性:")
    if stage_results['EIEM+EMBSFPN+RSCD (阶段3)']['final_mAP50'] < 0.3:
        print("  ❌ 完整模块训练不稳定，难以收敛")
        print("  🔧 可能原因: 模块间参数冲突、梯度消失、架构过于复杂")
    
    # 可视化对比
    create_staged_comparison_plots(stage_results)
    
    # 最终建议
    print(f"\n💡 最终建议")
    print("-"*40)
    
    print("基于分阶段验证结果:")
    
    if eiem_improvement > 10:
        print("1. ✅ **推荐使用C2f-EIEM模块** - 显著提升性能")
        print("   - 可以替代标准YOLOv8中的C2f模块")
        print("   - 边缘信息增强确实有效")
    
    if embsfpn_effect > -2:
        print("2. ⚠️ **简化EMBSFPN可以考虑** - 基本不损害性能")
        print("   - 但收益有限，可能不值得增加复杂度")
    
    if full_vs_baseline < -20:
        print("3. ❌ **不推荐完整三模块组合** - 性能严重下降")
        print("   - RSCD检测头可能存在设计问题")
        print("   - 三模块组合产生负效应")
    
    print("\n🎯 **最佳配置建议**:")
    if eiem_improvement > 5:
        print("推荐配置: YOLOv8n + C2f-EIEM")
        print("预期效果: 相比基线提升约{:.1f}%".format(eiem_improvement))
        print("优势: 简单有效，易于训练和部署")
    else:
        print("推荐配置: 标准YOLOv8n")
        print("原因: 自定义模块未显示明显优势")
    
    print("\n" + "="*80)

def create_staged_comparison_plots(stage_results):
    """创建分阶段对比图表"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('分阶段验证结果对比分析', fontsize=16, fontweight='bold')
    
    # 准备数据
    stages = list(stage_results.keys())
    map50_values = [stage_results[stage]['final_mAP50'] for stage in stages]
    map50_95_values = [stage_results[stage]['final_mAP50_95'] for stage in stages]
    precision_values = [stage_results[stage]['final_precision'] for stage in stages]
    recall_values = [stage_results[stage]['final_recall'] for stage in stages]
    
    # 定义颜色
    colors = ['#3498db', '#2ecc71', '#f39c12', '#e74c3c']
    
    # mAP50对比
    bars1 = axes[0,0].bar(range(len(stages)), map50_values, color=colors, alpha=0.7)
    axes[0,0].set_title('mAP50 对比', fontweight='bold')
    axes[0,0].set_ylabel('mAP50')
    axes[0,0].set_xticks(range(len(stages)))
    axes[0,0].set_xticklabels([s.split('(')[0].strip() for s in stages], rotation=45, ha='right')
    axes[0,0].grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, bar in enumerate(bars1):
        height = bar.get_height()
        axes[0,0].annotate(f'{height:.3f}',
                          xy=(bar.get_x() + bar.get_width() / 2, height),
                          xytext=(0, 3),
                          textcoords="offset points",
                          ha='center', va='bottom', fontweight='bold')
    
    # mAP50-95对比
    bars2 = axes[0,1].bar(range(len(stages)), map50_95_values, color=colors, alpha=0.7)
    axes[0,1].set_title('mAP50-95 对比', fontweight='bold')
    axes[0,1].set_ylabel('mAP50-95')
    axes[0,1].set_xticks(range(len(stages)))
    axes[0,1].set_xticklabels([s.split('(')[0].strip() for s in stages], rotation=45, ha='right')
    axes[0,1].grid(True, alpha=0.3)
    
    for i, bar in enumerate(bars2):
        height = bar.get_height()
        axes[0,1].annotate(f'{height:.3f}',
                          xy=(bar.get_x() + bar.get_width() / 2, height),
                          xytext=(0, 3),
                          textcoords="offset points",
                          ha='center', va='bottom', fontweight='bold')
    
    # Precision对比
    bars3 = axes[1,0].bar(range(len(stages)), precision_values, color=colors, alpha=0.7)
    axes[1,0].set_title('Precision 对比', fontweight='bold')
    axes[1,0].set_ylabel('Precision')
    axes[1,0].set_xticks(range(len(stages)))
    axes[1,0].set_xticklabels([s.split('(')[0].strip() for s in stages], rotation=45, ha='right')
    axes[1,0].grid(True, alpha=0.3)
    
    for i, bar in enumerate(bars3):
        height = bar.get_height()
        axes[1,0].annotate(f'{height:.3f}',
                          xy=(bar.get_x() + bar.get_width() / 2, height),
                          xytext=(0, 3),
                          textcoords="offset points",
                          ha='center', va='bottom', fontweight='bold')
    
    # 改进百分比对比
    baseline_map50 = stage_results['YOLOv8n (基线)']['final_mAP50']
    improvements = []
    stage_names = []
    
    for stage in stages[1:]:  # 跳过基线
        improvement = ((stage_results[stage]['final_mAP50'] - baseline_map50) / baseline_map50) * 100
        improvements.append(improvement)
        stage_names.append(stage.split('(')[0].strip())
    
    bar_colors = ['green' if x > 0 else 'red' for x in improvements]
    bars4 = axes[1,1].bar(range(len(improvements)), improvements, color=bar_colors, alpha=0.7)
    axes[1,1].set_title('相对基线的改进百分比', fontweight='bold')
    axes[1,1].set_ylabel('改进百分比 (%)')
    axes[1,1].set_xticks(range(len(improvements)))
    axes[1,1].set_xticklabels(stage_names, rotation=45, ha='right')
    axes[1,1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    axes[1,1].grid(True, alpha=0.3)
    
    for i, bar in enumerate(bars4):
        height = bar.get_height()
        axes[1,1].annotate(f'{height:+.1f}%',
                          xy=(bar.get_x() + bar.get_width() / 2, height),
                          xytext=(0, 3 if height >= 0 else -15),
                          textcoords="offset points",
                          ha='center', va='bottom' if height >= 0 else 'top',
                          fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('分阶段验证结果对比.png', dpi=300, bbox_inches='tight')
    print(f"\n📊 对比图表已保存: 分阶段验证结果对比.png")

if __name__ == "__main__":
    analyze_staged_validation() 