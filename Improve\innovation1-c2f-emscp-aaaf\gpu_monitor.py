#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🖥️ GPU利用率实时监控脚本
监控YOLO训练过程中的GPU使用情况
"""

import time
import subprocess
import json
import os
from datetime import datetime

def get_gpu_info():
    """获取GPU信息"""
    try:
        # 使用nvidia-smi获取GPU信息
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=index,name,memory.used,memory.total,utilization.gpu,utilization.memory,temperature.gpu,power.draw',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        gpu_info = []
        
        for line in lines:
            parts = [part.strip() for part in line.split(',')]
            if len(parts) >= 8:
                gpu_info.append({
                    'index': int(parts[0]),
                    'name': parts[1],
                    'memory_used': int(parts[2]),
                    'memory_total': int(parts[3]),
                    'gpu_util': int(parts[4]),
                    'memory_util': int(parts[5]),
                    'temperature': int(parts[6]),
                    'power_draw': float(parts[7])
                })
        
        return gpu_info
    
    except (subprocess.CalledProcessError, FileNotFoundError, ValueError) as e:
        print(f"❌ 获取GPU信息失败: {e}")
        return []

def get_process_info():
    """获取GPU进程信息"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-compute-apps=pid,process_name,used_memory',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        processes = []
        
        for line in lines:
            if line.strip():
                parts = [part.strip() for part in line.split(',')]
                if len(parts) >= 3:
                    processes.append({
                        'pid': int(parts[0]),
                        'name': parts[1],
                        'memory': int(parts[2])
                    })
        
        return processes
    
    except (subprocess.CalledProcessError, FileNotFoundError, ValueError):
        return []

def format_memory(mb):
    """格式化内存显示"""
    if mb >= 1024:
        return f"{mb/1024:.1f}GB"
    else:
        return f"{mb}MB"

def print_gpu_status():
    """打印GPU状态"""
    gpu_info = get_gpu_info()
    processes = get_process_info()
    
    if not gpu_info:
        print("❌ 无法获取GPU信息")
        return
    
    print("\n" + "="*80)
    print(f"🖥️  GPU监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    for gpu in gpu_info:
        memory_percent = (gpu['memory_used'] / gpu['memory_total']) * 100
        
        print(f"🎯 GPU {gpu['index']}: {gpu['name']}")
        print(f"   💾 显存: {format_memory(gpu['memory_used'])} / {format_memory(gpu['memory_total'])} ({memory_percent:.1f}%)")
        print(f"   ⚡ GPU利用率: {gpu['gpu_util']}%")
        print(f"   🧠 显存利用率: {gpu['memory_util']}%")
        print(f"   🌡️  温度: {gpu['temperature']}°C")
        print(f"   🔋 功耗: {gpu['power_draw']:.1f}W")
        
        # 显示利用率条形图
        gpu_bar = "█" * (gpu['gpu_util'] // 5) + "░" * (20 - gpu['gpu_util'] // 5)
        mem_bar = "█" * (int(memory_percent) // 5) + "░" * (20 - int(memory_percent) // 5)
        
        print(f"   📊 GPU: [{gpu_bar}] {gpu['gpu_util']}%")
        print(f"   📊 MEM: [{mem_bar}] {memory_percent:.1f}%")
        print()
    
    if processes:
        print("🔄 GPU进程:")
        for proc in processes:
            print(f"   PID {proc['pid']}: {proc['name']} - {format_memory(proc['memory'])}")
        print()
    
    # 性能评估
    if gpu_info:
        main_gpu = gpu_info[0]
        if main_gpu['gpu_util'] < 50:
            print("⚠️  GPU利用率较低 (<50%)，可能需要:")
            print("   • 增大batch size")
            print("   • 增大图像尺寸")
            print("   • 检查数据加载瓶颈")
        elif main_gpu['gpu_util'] > 90:
            print("🔥 GPU利用率很高 (>90%)，性能良好!")
        else:
            print("✅ GPU利用率正常 (50-90%)")

def monitor_continuous(interval=5):
    """连续监控"""
    print("🚀 开始GPU连续监控...")
    print("按 Ctrl+C 停止监控")
    
    try:
        while True:
            os.system('cls' if os.name == 'nt' else 'clear')  # 清屏
            print_gpu_status()
            time.sleep(interval)
    
    except KeyboardInterrupt:
        print("\n⏹️  监控已停止")

def monitor_once():
    """单次监控"""
    print_gpu_status()

def main():
    """主函数"""
    print("🖥️ GPU利用率监控工具")
    print("=" * 40)
    print("1. 单次检查")
    print("2. 连续监控 (5秒间隔)")
    print("3. 连续监控 (2秒间隔)")
    print("4. 连续监控 (10秒间隔)")
    
    choice = input("\n请选择监控模式 (1-4): ").strip()
    
    if choice == "1":
        monitor_once()
    elif choice == "2":
        monitor_continuous(5)
    elif choice == "3":
        monitor_continuous(2)
    elif choice == "4":
        monitor_continuous(10)
    else:
        print("❌ 无效选择，执行单次检查...")
        monitor_once()

if __name__ == "__main__":
    main()
