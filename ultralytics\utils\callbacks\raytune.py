# Ultralytics YOLO 🚀, AGPL-3.0 license

from ultralytics.utils import SETTINGS

try:
    assert SETTINGS["raytune"] is True  # verify integration is enabled
    import ray
    from ray import tune
    from ray.air import session

except (ImportError, AssertionError):
    tune = None


def on_fit_epoch_end(trainer):
    """Sends training metrics to <PERSON> Tune at end of each epoch."""
    try:
        # Try the new Ray API first
        if hasattr(ray.train, 'get_context') and ray.train.get_context():
            metrics = trainer.metrics
            metrics["epoch"] = trainer.epoch
            session.report(metrics)
        # Fallback to older API
        elif hasattr(ray.train, '_internal') and hasattr(ray.train._internal, 'session'):
            if hasattr(ray.train._internal.session, '_get_session'):
                if ray.train._internal.session._get_session():
                    metrics = trainer.metrics
                    metrics["epoch"] = trainer.epoch
                    session.report(metrics)
            elif hasattr(ray.train._internal.session, 'get_session'):
                if ray.train._internal.session.get_session():
                    metrics = trainer.metrics
                    metrics["epoch"] = trainer.epoch
                    session.report(metrics)
        # Final fallback - try to detect if we're in a Ray Tune session
        elif hasattr(ray, 'tune') and hasattr(ray.tune, 'is_session_enabled'):
            if ray.tune.is_session_enabled():
                metrics = trainer.metrics
                metrics["epoch"] = trainer.epoch
                session.report(metrics)
    except Exception as e:
        # Silently ignore Ray Tune callback errors to prevent training interruption
        pass


callbacks = (
    {
        "on_fit_epoch_end": on_fit_epoch_end,
    }
    if tune
    else {}
)
