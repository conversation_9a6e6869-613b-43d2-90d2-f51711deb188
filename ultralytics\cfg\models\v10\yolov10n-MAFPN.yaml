# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, SCDown, [512, 3, 2]] # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, SCDown, [1024, 3, 2]] # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SPP<PERSON>, [1024, 5]] # 9
  - [-1, 1, PSA, [1024]] # 10

# YOLOv10.0n head
head:
  - [6, 1, Conv, [256, 3, 2]] # 11-P5/32
  - [[-1, 10], 1, Concat, [1]] # 12
  - [-1, 3, C2fCIB, [1024, True, True]] # 13-P5/32

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] # 14-P4/16
  - [4, 1, Conv, [128, 3, 2]] # 15-P4/16
  - [[-1, -2, 6], 1, Concat, [1]] # 16
  - [-1, 3, C2f, [512]] # 17-P4/16

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] # 18-P3/8
  - [2, 1, Conv, [64, 3, 2]] # 19-P3/8
  - [[-1, -2, 4], 1, Concat, [1]] # 20
  - [-1, 3, C2f, [256]] # 21-P3/8

  - [[18, -1], 1, Concat, [1]] # 22
  - [-1, 3, C2f, [256]] # 23-P3/8

  - [21, 1, Conv, [256, 3, 2]] # 24-P4/16
  - [23, 1, Conv, [256, 3, 2]] # 25-P4/16
  - [[-1, -2, 17, 14], 1, Concat, [1]] # 26-P4/16
  - [-1, 3, C2f, [512]] # 27-P4/16

  - [17, 1, Conv, [512, 3, 2]] # 28-P5/32
  - [27, 1, Conv, [512, 3, 2]] # 29-P5/32
  - [[-1, -2, 13], 1, Concat, [1]] # 30-P5/32
  - [-1, 3, C2fCIB, [1024, True, True]] # 31-P5/32

  - [[23, 27, 31], 1, v10Detect, [nc]] # Detect(P3, P4, P5)