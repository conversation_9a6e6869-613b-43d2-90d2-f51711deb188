# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]

# 0-P1/2
# 1-P2/4
# 2-P3/8
# 3-P4/16
# 4-P5/32

backbone:
  # [from, repeats, module, args]
  - [-1, 1, EfficientViT_M0, []]  # 4
  - [-1, 1, SPPF, [1024, 5]]  # 5
  - [-1, 1, PSA, [1024]] # 6

# YOLOv10.0n head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] 
  - [[-1, 3], 1, Con<PERSON>, [1]] # cat backbone P4
  - [-1, 3, C2f, [512]] # 9

  - [-1, 1, nn.<PERSON>sa<PERSON>, [None, 2, "nearest"]]
  - [[-1, 2], 1, <PERSON><PERSON>, [1]] # cat backbone P3
  - [-1, 3, C2f, [256]] # 12 (P3/8-small)

  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 9], 1, Concat, [1]] # cat head P4
  - [-1, 3, C2f, [512]] # 15 (P4/16-medium)

  - [-1, 1, SCDown, [512, 3, 2]]
  - [[-1, 6], 1, Concat, [1]] # cat head P5
  - [-1, 3, C2fCIB, [1024, True, True]] # 18 (P5/32-large)

  - [[12, 15, 18], 1, v10Detect, [nc]] # Detect(P3, P4, P5)