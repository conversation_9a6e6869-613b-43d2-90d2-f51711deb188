#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建训练结果对比的总结图表
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_summary_comparison():
    """创建性能对比总结图表"""
    
    # 数据
    metrics = ['mAP@0.5', 'mAP@0.5:0.95', 'Precision', 'Recall']
    baseline_values = [0.75445, 0.43997, 0.75945, 0.68415]
    optimized_values = [0.76830, 0.43312, 0.76499, 0.70380]
    improvements = [1.84, -1.56, 0.73, 2.87]
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 左侧：性能对比柱状图
    x = np.arange(len(metrics))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, baseline_values, width, label='基准模型 (YOLOv8n4)', 
                   color='#3498db', alpha=0.8)
    bars2 = ax1.bar(x + width/2, optimized_values, width, label='BiFPN优化模型', 
                   color='#e74c3c', alpha=0.8)
    
    ax1.set_xlabel('评估指标')
    ax1.set_ylabel('分数')
    ax1.set_title('性能指标对比', fontsize=14, fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(metrics)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 在柱状图上添加数值标签
    for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
        height1 = bar1.get_height()
        height2 = bar2.get_height()
        ax1.text(bar1.get_x() + bar1.get_width()/2., height1 + 0.01,
                f'{height1:.3f}', ha='center', va='bottom', fontsize=10)
        ax1.text(bar2.get_x() + bar2.get_width()/2., height2 + 0.01,
                f'{height2:.3f}', ha='center', va='bottom', fontsize=10)
    
    # 右侧：改进幅度柱状图
    colors = ['#2ecc71' if x > 0 else '#e74c3c' for x in improvements]
    bars3 = ax2.bar(metrics, improvements, color=colors, alpha=0.8)
    
    ax2.set_xlabel('评估指标')
    ax2.set_ylabel('改进幅度 (%)')
    ax2.set_title('改进幅度对比', fontsize=14, fontweight='bold')
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax2.grid(True, alpha=0.3)
    
    # 在柱状图上添加数值标签
    for bar, improvement in zip(bars3, improvements):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + (0.1 if height > 0 else -0.3),
                f'{improvement:+.1f}%', ha='center', va='bottom' if height > 0 else 'top',
                fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('performance_summary.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 创建训练轮数对比图
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    categories = ['收敛轮数', '最佳性能', '最终性能', '稳定性']
    baseline_data = [98, 0.75557, 0.75445, 0.002]
    optimized_data = [69, 0.77182, 0.76830, 0.001]
    
    x = np.arange(len(categories))
    width = 0.35
    
    # 标准化数据用于显示
    baseline_normalized = [98/100, 0.75557, 0.75445, 0.002*100]
    optimized_normalized = [69/100, 0.77182, 0.76830, 0.001*100]
    
    bars1 = ax.bar(x - width/2, baseline_normalized, width, label='基准模型', 
                   color='#3498db', alpha=0.8)
    bars2 = ax.bar(x + width/2, optimized_normalized, width, label='BiFPN优化模型', 
                   color='#e74c3c', alpha=0.8)
    
    ax.set_xlabel('训练特征')
    ax.set_ylabel('标准化分数')
    ax.set_title('训练过程特征对比', fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(categories)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加原始数值标签
    original_labels = [
        [f'{baseline_data[i]:.0f}' if i == 0 else f'{baseline_data[i]:.3f}' for i in range(len(baseline_data))],
        [f'{optimized_data[i]:.0f}' if i == 0 else f'{optimized_data[i]:.3f}' for i in range(len(optimized_data))]
    ]
    
    for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
        height1 = bar1.get_height()
        height2 = bar2.get_height()
        ax.text(bar1.get_x() + bar1.get_width()/2., height1 + 0.01,
                original_labels[0][i], ha='center', va='bottom', fontsize=10)
        ax.text(bar2.get_x() + bar2.get_width()/2., height2 + 0.01,
                original_labels[1][i], ha='center', va='bottom', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('training_features_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    create_summary_comparison()
    print("✅ 总结图表生成完成！")
    print("  - performance_summary.png: 性能指标对比")
    print("  - training_features_comparison.png: 训练特征对比") 