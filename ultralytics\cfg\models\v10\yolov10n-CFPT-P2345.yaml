# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, SCDown, [512, 3, 2]] # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, SCDown, [1024, 3, 2]] # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SPP<PERSON>, [1024, 5]] # 9
  - [-1, 1, PSA, [1024]] # 10

head:
  - [2, 1, Conv, [128]]  # 11-P2/4
  - [4, 1, Conv, [128]]  # 12-P3/8
  - [6, 1, Conv, [128]]  # 13-P4/16
  - [10, 1, Conv, [128]]  # 14-P5/32

  - [[11, 12, 13, 14], 1, CrossLayerChannelAttention, [4]] # 15
  - [15, 1, GetIndexOutput, [0]] # 16-P2/4
  - [15, 1, GetIndexOutput, [1]] # 17-P3/8
  - [15, 1, GetIndexOutput, [2]] # 18-P4/16
  - [15, 1, GetIndexOutput, [3]] # 19-P5/32

  - [[16, 17, 18, 19], 1, CrossLayerSpatialAttention, [4]] # 20
  - [20, 1, GetIndexOutput, [0]] # 21-P2/4
  - [20, 1, GetIndexOutput, [1]] # 22-P3/8
  - [20, 1, GetIndexOutput, [2]] # 23-P4/16
  - [20, 1, GetIndexOutput, [3]] # 24-P5/32

  - [[11, 21], 1, Add, []] # 25-P2/4
  - [[12, 22], 1, Add, []] # 26-P3/8
  - [[13, 23], 1, Add, []] # 27-P4/16
  - [[14, 24], 1, Add, []] # 28-P5/32

  - [[25, 26, 27, 28], 1, v10Detect, [nc]] # Detect(P2, P3, P4, P5)