---
description: Explore detailed descriptions and implementations of various loss functions used in Ultralytics models, including Varifocal Loss, Focal Loss, Bbox Loss, and more.
keywords: Ultralytics, loss functions, Varifocal Loss, Focal Loss, Bbox Loss, Rotated Bbox Loss, Keypoint Loss, YOLO, model training, documentation
---

# Reference for `ultralytics/utils/loss.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/loss.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/loss.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/loss.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.loss.VarifocalLoss

<br><br>

## ::: ultralytics.utils.loss.FocalLoss

<br><br>

## ::: ultralytics.utils.loss.DFLoss

<br><br>

## ::: ultralytics.utils.loss.BboxLoss

<br><br>

## ::: ultralytics.utils.loss.RotatedBboxLoss

<br><br>

## ::: ultralytics.utils.loss.KeypointLoss

<br><br>

## ::: ultralytics.utils.loss.v8DetectionLoss

<br><br>

## ::: ultralytics.utils.loss.v8SegmentationLoss

<br><br>

## ::: ultralytics.utils.loss.v8PoseLoss

<br><br>

## ::: ultralytics.utils.loss.v8ClassificationLoss

<br><br>

## ::: ultralytics.utils.loss.v8OBBLoss

<br><br>

## ::: ultralytics.utils.loss.E2EDetectLoss

<br><br>
