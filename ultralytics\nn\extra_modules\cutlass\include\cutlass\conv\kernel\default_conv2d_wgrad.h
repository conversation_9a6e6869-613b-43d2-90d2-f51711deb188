/***************************************************************************************************
 * Copyright (c) 2017-2020, NVIDIA CORPORATION.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 *modification, are permitted provided that the following conditions are met:
 *     * Redistributions of source code must retain the above copyright notice,
 *this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above copyright
 *notice, this list of conditions and the following disclaimer in the
 *documentation and/or other materials provided with the distribution.
 *     * Neither the name of the NVIDIA CORPORATION nor the names of its
 *contributors may be used to endorse or promote products derived from this
 *software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 *AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 *IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 *DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE FOR ANY DIRECT,
 *INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 *OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TOR (INCLUDING
 *NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 *EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 **************************************************************************************************/

/*! \file
    \brief
    Default kernel-level implicit GEMM convolution definitions combine
   threadblock-scoped matrix multiply-add with the appropriate
   threadblock-scoped epilogue.
*/

#pragma once

#include "cutlass/cutlass.h"
#include "cutlass/conv/kernel/default_conv2d.h"

#include "cutlass/conv/threadblock/conv2d_wgrad_output_gradient_tile_access_iterator_analytic.h"
#include "cutlass/conv/threadblock/conv2d_wgrad_activation_tile_access_iterator_analytic.h"
#include "cutlass/conv/threadblock/conv2d_wgrad_output_gradient_tile_access_iterator_optimized.h"
#include "cutlass/conv/threadblock/conv2d_wgrad_activation_tile_access_iterator_optimized.h"
#include "cutlass/conv/threadblock/conv2d_tile_iterator.h"

/////////////////////////////////////////////////////////////////////////////////////////////////

namespace cutlass {
namespace conv {
namespace kernel {

/////////////////////////////////////////////////////////////////////////////////////////////////

/// Defines a kernel for Conv2dWgrad
template <typename ElementA, typename LayoutA, typename ElementB,
          typename LayoutB, typename ElementC, typename LayoutC,
          typename ElementAccumulator, typename OperatorClass, typename ArchTag,
          typename ThreadblockShape, typename WarpShape,
          typename InstructionShape, typename EpilogueOutputOp,
          typename ThreadblockSwizzle, int Stages, typename MathOperatorTag,
          conv::IteratorAlgorithm IteratorAlgorithm =
                  IteratorAlgorithm::kAnalytic,
          conv::StrideSupport StrideSupport = StrideSupport::kStrided>
struct DefaultConv2dWgrad;
/////////////////////////////////////////////////////////////////////////////////////////////////

/////////////////////////////////////////////////////////////////////////////////////////////////
//                          OpClassTensorOp convolutions
/////////////////////////////////////////////////////////////////////////////////////////////////

/// Defines a kernel for Conv2dWgrad specialzation for Analytic
/// IteratorAlgorithm and multistage
// pipeline.
template <typename ElementA, typename LayoutA, typename ElementB,
          typename LayoutB, typename ElementC, typename LayoutC,
          typename ElementAccumulator, typename OperatorClass, typename ArchTag,
          typename ThreadblockShape, typename WarpShape,
          typename InstructionShape, typename EpilogueOutputOp,
          typename ThreadblockSwizzle, int Stages, typename MathOperatorTag>
struct DefaultConv2dWgrad<ElementA, LayoutA, ElementB, LayoutB, ElementC,
                          LayoutC, ElementAccumulator, OperatorClass, ArchTag,
                          ThreadblockShape, WarpShape, InstructionShape,
                          EpilogueOutputOp, ThreadblockSwizzle, Stages,
                          MathOperatorTag, IteratorAlgorithm::kAnalytic> {
    // Define the core components from GEMM
    using MmaCore = typename cutlass::gemm::threadblock::DefaultMmaCore<
            ThreadblockShape, WarpShape, InstructionShape, ElementA,
            layout::ColumnMajor, ElementB, layout::RowMajor, ElementAccumulator,
            layout::RowMajor, OperatorClass, Stages, MathOperatorTag>;

    // Define iterators over tiles from the A operand
    using ThreadMapA = typename MmaCore::IteratorThreadMapA;
    using IteratorA = cutlass::conv::threadblock::
            Conv2dWgradOutputGradientTileAccessIteratorAnalytic<
                    cutlass::MatrixShape<ThreadblockShape::kM,
                                         ThreadblockShape::kK>,
                    ElementA, ThreadMapA>;

    using SmemIteratorA = typename MmaCore::SmemIteratorA;

    // Define iterators over tiles from the B operand
    using ThreadMapB = typename MmaCore::IteratorThreadMapB;
    using IteratorB = cutlass::conv::threadblock::
            Conv2dWgradActivationTileAccessIteratorAnalytic<
                    cutlass::MatrixShape<ThreadblockShape::kK,
                                         ThreadblockShape::kN>,
                    ElementB, ThreadMapB>;

    using SmemIteratorB = typename MmaCore::SmemIteratorB;

    // Warp-level GEMM components
    using WarpMmaTensorOp = typename MmaCore::MmaTensorOp;
    using MmaPolicy = typename MmaCore::MmaPolicy;

    // Define the Mma
    using Mma = threadblock::ImplicitGemmMultistage<
            ThreadblockShape, IteratorA, SmemIteratorA,
            arch::CacheOperation::Always, IteratorB, SmemIteratorB,
            arch::CacheOperation::Always, MmaPolicy, Stages>;

    // Define the epilogue
    using Epilogue = typename epilogue::threadblock::DefaultEpilogueTensorOp<
            ThreadblockShape, WarpMmaTensorOp, 1, EpilogueOutputOp,
            EpilogueOutputOp::kCount>::Epilogue;

    // Define the kernel
    using Kernel = cutlass::conv::kernel::ImplicitGemmConvolution<
            Mma, Epilogue, ThreadblockSwizzle, conv::Operator::kWgrad>;
};
/////////////////////////////////////////////////////////////////////////////////////////////////

/// Defines a kernel for Conv2dWgrad specialzation for Analytic
/// IteratorAlgorithm and two
// pipeline.
template <typename ElementA, typename LayoutA, typename ElementB,
          typename LayoutB, typename ElementC, typename LayoutC,
          typename ElementAccumulator, typename OperatorClass, typename ArchTag,
          typename ThreadblockShape, typename WarpShape,
          typename InstructionShape, typename EpilogueOutputOp,
          typename ThreadblockSwizzle, typename MathOperatorTag>
struct DefaultConv2dWgrad<ElementA, LayoutA, ElementB, LayoutB, ElementC,
                          LayoutC, ElementAccumulator, OperatorClass, ArchTag,
                          ThreadblockShape, WarpShape, InstructionShape,
                          EpilogueOutputOp, ThreadblockSwizzle, 2,
                          MathOperatorTag, IteratorAlgorithm::kAnalytic> {
    // Define the core components from GEMM
    using MmaCore = typename cutlass::gemm::threadblock::DefaultMmaCore<
            ThreadblockShape, WarpShape, InstructionShape, ElementA,
            layout::ColumnMajor, ElementB, layout::RowMajor, ElementAccumulator,
            layout::RowMajor, OperatorClass, 2, MathOperatorTag>;

    // Define iterators over tiles from the A operand
    using ThreadMapA = typename MmaCore::IteratorThreadMapA;
    using IteratorA = cutlass::conv::threadblock::TileIterator<
            cutlass::conv::threadblock::
                    Conv2dWgradOutputGradientTileAccessIteratorAnalytic<
                            cutlass::MatrixShape<ThreadblockShape::kM,
                                                 ThreadblockShape::kK>,
                            ElementA, ThreadMapA> >;

    using SmemIteratorA = typename MmaCore::SmemIteratorA;

    // Define iterators over tiles from the B operand
    using ThreadMapB = typename MmaCore::IteratorThreadMapB;
    using IteratorB = cutlass::conv::threadblock::TileIterator<
            cutlass::conv::threadblock::
                    Conv2dWgradActivationTileAccessIteratorAnalytic<
                            cutlass::MatrixShape<ThreadblockShape::kK,
                                                 ThreadblockShape::kN>,
                            ElementB, ThreadMapB> >;

    using SmemIteratorB = typename MmaCore::SmemIteratorB;

    // Warp-level GEMM components
    using WarpMmaTensorOp = typename MmaCore::MmaTensorOp;
    using MmaPolicy = typename MmaCore::MmaPolicy;

    // Define the Mma
    using Mma = threadblock::ImplicitGemmPipelined<
            ThreadblockShape, IteratorA, SmemIteratorA, IteratorB,
            SmemIteratorB, ElementC, LayoutC, MmaPolicy>;

    // Define the epilogue
    using Epilogue =
            typename detail::DefaultConvEpilogue<ArchTag, ThreadblockShape,
                                                 WarpMmaTensorOp, 1,
                                                 EpilogueOutputOp>::Epilogue;

    // Define the kernel
    using Kernel = cutlass::conv::kernel::ImplicitGemmConvolution<
            Mma, Epilogue, ThreadblockSwizzle, conv::Operator::kWgrad>;
};

/////////////////////////////////////////////////////////////////////////////////////////////////

/// Defines a kernel for Conv2dWgrad specialzation for Optimized
/// IteratorAlgorithm and multistage
// pipeline.
template <typename ElementA, typename LayoutA, typename ElementB,
          typename LayoutB, typename ElementC, typename LayoutC,
          typename ElementAccumulator, typename OperatorClass, typename ArchTag,
          typename ThreadblockShape, typename WarpShape,
          typename InstructionShape, typename EpilogueOutputOp,
          typename ThreadblockSwizzle, int Stages, typename MathOperatorTag>
struct DefaultConv2dWgrad<ElementA, LayoutA, ElementB, LayoutB, ElementC,
                          LayoutC, ElementAccumulator, OperatorClass, ArchTag,
                          ThreadblockShape, WarpShape, InstructionShape,
                          EpilogueOutputOp, ThreadblockSwizzle, Stages,
                          MathOperatorTag, IteratorAlgorithm::kOptimized> {
    // Define the core components from GEMM
    using MmaCore = typename cutlass::gemm::threadblock::DefaultMmaCore<
            ThreadblockShape, WarpShape, InstructionShape, ElementA,
            layout::ColumnMajor, ElementB, layout::RowMajor, ElementAccumulator,
            layout::RowMajor, OperatorClass, Stages, MathOperatorTag>;

    // Define iterators over tiles from the A operand
    using ThreadMapA = typename MmaCore::IteratorThreadMapA;
    using IteratorA = cutlass::conv::threadblock::
            Conv2dWgradOutputGradientTileAccessIteratorOptimized<
                    cutlass::MatrixShape<ThreadblockShape::kM,
                                         ThreadblockShape::kK>,
                    ElementA, ThreadMapA>;

    using SmemIteratorA = typename MmaCore::SmemIteratorA;

    // Define iterators over tiles from the B operand
    using ThreadMapB = typename MmaCore::IteratorThreadMapB;
    using IteratorB = cutlass::conv::threadblock::
            Conv2dWgradActivationTileAccessIteratorOptimized<
                    cutlass::MatrixShape<ThreadblockShape::kK,
                                         ThreadblockShape::kN>,
                    ElementB, ThreadMapB>;

    using SmemIteratorB = typename MmaCore::SmemIteratorB;

    // Warp-level GEMM components
    using WarpMmaTensorOp = typename MmaCore::MmaTensorOp;
    using MmaPolicy = typename MmaCore::MmaPolicy;

    // Define the Mma
    using Mma = threadblock::ImplicitGemmMultistage<
            ThreadblockShape, IteratorA, SmemIteratorA,
            arch::CacheOperation::Always, IteratorB, SmemIteratorB,
            arch::CacheOperation::Always, MmaPolicy, Stages>;

    // Define the epilogue
    using Epilogue = typename epilogue::threadblock::DefaultEpilogueTensorOp<
            ThreadblockShape, WarpMmaTensorOp, 1, EpilogueOutputOp,
            EpilogueOutputOp::kCount>::Epilogue;

    // Define the kernel
    using Kernel = cutlass::conv::kernel::ImplicitGemmConvolution<
            Mma, Epilogue, ThreadblockSwizzle, conv::Operator::kWgrad>;
};
/////////////////////////////////////////////////////////////////////////////////////////////////

/// Defines a kernel for Conv2dWgrad specialzation for Optimized
/// IteratorAlgorithm and two
// pipeline.
template <typename ElementA, typename LayoutA, typename ElementB,
          typename LayoutB, typename ElementC, typename LayoutC,
          typename ElementAccumulator, typename OperatorClass, typename ArchTag,
          typename ThreadblockShape, typename WarpShape,
          typename InstructionShape, typename EpilogueOutputOp,
          typename ThreadblockSwizzle, typename MathOperatorTag>
struct DefaultConv2dWgrad<ElementA, LayoutA, ElementB, LayoutB, ElementC,
                          LayoutC, ElementAccumulator, OperatorClass, ArchTag,
                          ThreadblockShape, WarpShape, InstructionShape,
                          EpilogueOutputOp, ThreadblockSwizzle, 2,
                          MathOperatorTag, IteratorAlgorithm::kOptimized> {
    // Define the core components from GEMM
    using MmaCore = typename cutlass::gemm::threadblock::DefaultMmaCore<
            ThreadblockShape, WarpShape, InstructionShape, ElementA,
            layout::ColumnMajor, ElementB, layout::RowMajor, ElementAccumulator,
            layout::RowMajor, OperatorClass, 2, MathOperatorTag>;

    // Define iterators over tiles from the A operand
    using ThreadMapA = typename MmaCore::IteratorThreadMapA;
    using IteratorA = cutlass::conv::threadblock::TileIterator<
            cutlass::conv::threadblock::
                    Conv2dWgradOutputGradientTileAccessIteratorOptimized<
                            cutlass::MatrixShape<ThreadblockShape::kM,
                                                 ThreadblockShape::kK>,
                            ElementA, ThreadMapA> >;

    using SmemIteratorA = typename MmaCore::SmemIteratorA;

    // Define iterators over tiles from the B operand
    using ThreadMapB = typename MmaCore::IteratorThreadMapB;
    using IteratorB = cutlass::conv::threadblock::TileIterator<
            cutlass::conv::threadblock::
                    Conv2dWgradActivationTileAccessIteratorOptimized<
                            cutlass::MatrixShape<ThreadblockShape::kK,
                                                 ThreadblockShape::kN>,
                            ElementB, ThreadMapB> >;

    using SmemIteratorB = typename MmaCore::SmemIteratorB;

    // Warp-level GEMM components
    using WarpMmaTensorOp = typename MmaCore::MmaTensorOp;
    using MmaPolicy = typename MmaCore::MmaPolicy;

    // Define the Mma
    using Mma = threadblock::ImplicitGemmPipelined<
            ThreadblockShape, IteratorA, SmemIteratorA, IteratorB,
            SmemIteratorB, ElementC, LayoutC, MmaPolicy>;

    // Define the epilogue
    using Epilogue =
            typename detail::DefaultConvEpilogue<ArchTag, ThreadblockShape,
                                                 WarpMmaTensorOp, 1,
                                                 EpilogueOutputOp>::Epilogue;

    // Define the kernel
    using Kernel = cutlass::conv::kernel::ImplicitGemmConvolution<
            Mma, Epilogue, ThreadblockSwizzle, conv::Operator::kWgrad>;
};

/////////////////////////////////////////////////////////////////////////////////////////////////
//                         OpClassSimt convolutions
/////////////////////////////////////////////////////////////////////////////////////////////////
/// Defines a kernel for Conv2dWgrad specialzation for Analytic
/// IteratorAlgorithm, multi-stage pipeline, and FFMA-based mainloop for SM80

template <typename ElementA, typename LayoutA, typename ElementB,
          typename LayoutB, typename ElementC, typename LayoutC,
          typename ElementAccumulator, typename ArchTag,
          typename ThreadblockShape, typename WarpShape,
          typename InstructionShape, typename EpilogueOutputOp,
          typename ThreadblockSwizzle, int Stages, typename MathOperatorTag>
struct DefaultConv2dWgrad<
        ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,
        ElementAccumulator, arch::OpClassSimt, ArchTag, ThreadblockShape,
        WarpShape, InstructionShape, EpilogueOutputOp, ThreadblockSwizzle,
        Stages, MathOperatorTag, IteratorAlgorithm::kAnalytic> {
    // Define the core components from GEMM
    using MmaCore = typename cutlass::gemm::threadblock::DefaultMmaCore<
            ThreadblockShape, WarpShape, InstructionShape, ElementA,
            layout::ColumnMajor, ElementB, layout::RowMajor, ElementAccumulator,
            layout::RowMajor, arch::OpClassSimt, Stages, MathOperatorTag>;

    // Define iterators over tiles from the A operand
    using ThreadMapA = typename MmaCore::IteratorThreadMapA;
    using IteratorA = cutlass::conv::threadblock::
            Conv2dWgradOutputGradientTileAccessIteratorAnalytic<
                    cutlass::MatrixShape<ThreadblockShape::kM,
                                         ThreadblockShape::kK>,
                    ElementA, ThreadMapA>;

    using SmemIteratorA = typename MmaCore::SmemIteratorA;

    // Define iterators over tiles from the B operand
    using ThreadMapB = typename MmaCore::IteratorThreadMapB;
    using IteratorB = cutlass::conv::threadblock::
            Conv2dWgradActivationTileAccessIteratorAnalytic<
                    cutlass::MatrixShape<ThreadblockShape::kK,
                                         ThreadblockShape::kN>,
                    ElementB, ThreadMapB>;

    using SmemIteratorB = typename MmaCore::SmemIteratorB;

    // Warp-level GEMM components
    using WarpMmaSimtOp = typename MmaCore::MmaWarpSimt;
    using MmaPolicy = typename MmaCore::MmaPolicy;

    // Define the Mma
    using Mma = threadblock::ImplicitGemmMultistage<
            ThreadblockShape, IteratorA, SmemIteratorA,
            arch::CacheOperation::Always, IteratorB, SmemIteratorB,
            arch::CacheOperation::Always, MmaPolicy, Stages>;

    // Define the epilogue
    using Epilogue = typename epilogue::threadblock::DefaultEpilogueSimt<
            ThreadblockShape, WarpMmaSimtOp, EpilogueOutputOp,
            EpilogueOutputOp::kCount>::Epilogue;

    // Define the kernel
    using Kernel = cutlass::conv::kernel::ImplicitGemmConvolution<
            Mma, Epilogue, ThreadblockSwizzle, conv::Operator::kWgrad>;
};

/////////////////////////////////////////////////////////////////////////////////////////////////

/// Defines a kernel for Conv2dWgrad specialzation for Optimized
/// IteratorAlgorithm, multi-stage pipeline, and FFMA-based mainloop for SM80

template <typename ElementA, typename LayoutA, typename ElementB,
          typename LayoutB, typename ElementC, typename LayoutC,
          typename ElementAccumulator, typename ArchTag,
          typename ThreadblockShape, typename WarpShape,
          typename InstructionShape, typename EpilogueOutputOp,
          typename ThreadblockSwizzle, int Stages, typename MathOperatorTag>
struct DefaultConv2dWgrad<
        ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,
        ElementAccumulator, arch::OpClassSimt, ArchTag, ThreadblockShape,
        WarpShape, InstructionShape, EpilogueOutputOp, ThreadblockSwizzle,
        Stages, MathOperatorTag, IteratorAlgorithm::kOptimized> {
    // Define the core components from GEMM
    using MmaCore = typename cutlass::gemm::threadblock::DefaultMmaCore<
            ThreadblockShape, WarpShape, InstructionShape, ElementA,
            layout::ColumnMajor, ElementB, layout::RowMajor, ElementAccumulator,
            layout::RowMajor, arch::OpClassSimt, Stages, MathOperatorTag>;

    // Define iterators over tiles from the A operand
    using ThreadMapA = typename MmaCore::IteratorThreadMapA;
    using IteratorA = cutlass::conv::threadblock::
            Conv2dWgradOutputGradientTileAccessIteratorOptimized<
                    cutlass::MatrixShape<ThreadblockShape::kM,
                                         ThreadblockShape::kK>,
                    ElementA, ThreadMapA>;

    using SmemIteratorA = typename MmaCore::SmemIteratorA;

    // Define iterators over tiles from the B operand
    using ThreadMapB = typename MmaCore::IteratorThreadMapB;
    using IteratorB = cutlass::conv::threadblock::
            Conv2dWgradActivationTileAccessIteratorOptimized<
                    cutlass::MatrixShape<ThreadblockShape::kK,
                                         ThreadblockShape::kN>,
                    ElementB, ThreadMapB>;

    using SmemIteratorB = typename MmaCore::SmemIteratorB;

    // Warp-level GEMM components
    using WarpMmaSimtOp = typename MmaCore::MmaWarpSimt;
    using MmaPolicy = typename MmaCore::MmaPolicy;

    // Define the Mma
    using Mma = threadblock::ImplicitGemmMultistage<
            ThreadblockShape, IteratorA, SmemIteratorA,
            arch::CacheOperation::Always, IteratorB, SmemIteratorB,
            arch::CacheOperation::Always, MmaPolicy, Stages>;

    // Define the epilogue
    using Epilogue = typename epilogue::threadblock::DefaultEpilogueSimt<
            ThreadblockShape, WarpMmaSimtOp, EpilogueOutputOp,
            EpilogueOutputOp::kCount>::Epilogue;

    // Define the kernel
    using Kernel = cutlass::conv::kernel::ImplicitGemmConvolution<
            Mma, Epilogue, ThreadblockSwizzle, conv::Operator::kWgrad>;
};

/////////////////////////////////////////////////////////////////////////////////////////////////

/// Defines a kernel for Conv2dWgrad specialzation for Analytic
/// IteratorAlgorithm, 2 stage pipeline, and FFMA-based mainloop for SM50
template <typename ElementA, typename LayoutA, typename ElementB,
          typename LayoutB, typename ElementC, typename LayoutC,
          typename ElementAccumulator, typename ArchTag,
          typename ThreadblockShape, typename WarpShape,
          typename InstructionShape, typename EpilogueOutputOp,
          typename ThreadblockSwizzle, typename MathOperatorTag>
struct DefaultConv2dWgrad<
        ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,
        ElementAccumulator, arch::OpClassSimt, ArchTag, ThreadblockShape,
        WarpShape, InstructionShape, EpilogueOutputOp, ThreadblockSwizzle, 2,
        MathOperatorTag, IteratorAlgorithm::kAnalytic> {
    // Define the core components from GEMM
    using MmaCore = typename cutlass::gemm::threadblock::DefaultMmaCore<
            ThreadblockShape, WarpShape, InstructionShape, ElementA,
            layout::ColumnMajor, ElementB, layout::RowMajor, ElementAccumulator,
            layout::RowMajor, arch::OpClassSimt, 2, MathOperatorTag>;

    // Define iterators over tiles from the A operand
    using ThreadMapA = typename MmaCore::IteratorThreadMapA;
    using IteratorA = cutlass::conv::threadblock::TileIterator<
            cutlass::conv::threadblock::
                    Conv2dWgradOutputGradientTileAccessIteratorAnalytic<
                            cutlass::MatrixShape<ThreadblockShape::kM,
                                                 ThreadblockShape::kK>,
                            ElementA, ThreadMapA> >;

    using SmemIteratorA = typename MmaCore::SmemIteratorA;

    // Define iterators over tiles from the B operand
    using ThreadMapB = typename MmaCore::IteratorThreadMapB;
    using IteratorB = cutlass::conv::threadblock::TileIterator<
            cutlass::conv::threadblock::
                    Conv2dWgradActivationTileAccessIteratorAnalytic<
                            cutlass::MatrixShape<ThreadblockShape::kK,
                                                 ThreadblockShape::kN>,
                            ElementB, ThreadMapB> >;

    using SmemIteratorB = typename MmaCore::SmemIteratorB;

    // Warp-level GEMM components
    using WarpMmaSimtOp = typename MmaCore::MmaWarpSimt;
    using MmaPolicy = typename MmaCore::MmaPolicy;

    // Define the Mma
    using Mma = threadblock::ImplicitGemmPipelined<
            ThreadblockShape, IteratorA, SmemIteratorA, IteratorB,
            SmemIteratorB, ElementC, LayoutC, MmaPolicy>;

    // Define the epilogue
    using Epilogue = typename epilogue::threadblock::DefaultEpilogueSimt<
            ThreadblockShape, WarpMmaSimtOp, EpilogueOutputOp,
            EpilogueOutputOp::kCount>::Epilogue;

    // Define the kernel
    using Kernel = cutlass::conv::kernel::ImplicitGemmConvolution<
            Mma, Epilogue, ThreadblockSwizzle, conv::Operator::kWgrad>;
};

/////////////////////////////////////////////////////////////////////////////////////////////////

/// Defines a kernel for Conv2dWgrad specialzation for Optimized
/// IteratorAlgorithm, 2 stage pipeline, and FFMA-based mainloop for SM50
template <typename ElementA, typename LayoutA, typename ElementB,
          typename LayoutB, typename ElementC, typename LayoutC,
          typename ElementAccumulator, typename ArchTag,
          typename ThreadblockShape, typename WarpShape,
          typename InstructionShape, typename EpilogueOutputOp,
          typename ThreadblockSwizzle, typename MathOperatorTag>
struct DefaultConv2dWgrad<
        ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC,
        ElementAccumulator, arch::OpClassSimt, ArchTag, ThreadblockShape,
        WarpShape, InstructionShape, EpilogueOutputOp, ThreadblockSwizzle, 2,
        MathOperatorTag, IteratorAlgorithm::kOptimized> {
    // Define the core components from GEMM
    using MmaCore = typename cutlass::gemm::threadblock::DefaultMmaCore<
            ThreadblockShape, WarpShape, InstructionShape, ElementA,
            layout::ColumnMajor, ElementB, layout::RowMajor, ElementAccumulator,
            layout::RowMajor, arch::OpClassSimt, 2, MathOperatorTag>;

    // Define iterators over tiles from the A operand
    using ThreadMapA = typename MmaCore::IteratorThreadMapA;
    using IteratorA = cutlass::conv::threadblock::TileIterator<
            cutlass::conv::threadblock::
                    Conv2dWgradOutputGradientTileAccessIteratorOptimized<
                            cutlass::MatrixShape<ThreadblockShape::kM,
                                                 ThreadblockShape::kK>,
                            ElementA, ThreadMapA> >;

    using SmemIteratorA = typename MmaCore::SmemIteratorA;

    // Define iterators over tiles from the B operand
    using ThreadMapB = typename MmaCore::IteratorThreadMapB;
    using IteratorB = cutlass::conv::threadblock::TileIterator<
            cutlass::conv::threadblock::
                    Conv2dWgradActivationTileAccessIteratorOptimized<
                            cutlass::MatrixShape<ThreadblockShape::kK,
                                                 ThreadblockShape::kN>,
                            ElementB, ThreadMapB> >;

    using SmemIteratorB = typename MmaCore::SmemIteratorB;

    // Warp-level GEMM components
    using WarpMmaSimtOp = typename MmaCore::MmaWarpSimt;
    using MmaPolicy = typename MmaCore::MmaPolicy;

    // Define the Mma
    using Mma = threadblock::ImplicitGemmPipelined<
            ThreadblockShape, IteratorA, SmemIteratorA, IteratorB,
            SmemIteratorB, ElementC, LayoutC, MmaPolicy>;

    // Define the epilogue
    using Epilogue = typename epilogue::threadblock::DefaultEpilogueSimt<
            ThreadblockShape, WarpMmaSimtOp, EpilogueOutputOp,
            EpilogueOutputOp::kCount>::Epilogue;

    // Define the kernel
    using Kernel = cutlass::conv::kernel::ImplicitGemmConvolution<
            Mma, Epilogue, ThreadblockSwizzle, conv::Operator::kWgrad>;
};
/////////////////////////////////////////////////////////////////////////////////////////////////

}  // namespace kernel
}  // namespace conv
}  // namespace cutlass

/////////////////////////////////////////////////////////////////////////////////////////////////
