---
description: Explore the live inference capabilities of Streamlit combined with Ultralytics YOLOv8. Learn to implement real-time object detection in your web applications with our comprehensive guide.
keywords: Ultralytics, YOLOv8, live inference, real-time object detection, Streamlit, computer vision, webcam inference, object detection, Python, ML, cv2
---

# Reference for `ultralytics/solutions/streamlit_inference.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/streamlit_inference.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/streamlit_inference.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/streamlit_inference.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.solutions.streamlit_inference.inference

<br><br>
