/***************************************************************************************************
 * Copyright (c) 2017-2020, NVIDIA CORPORATION.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 *modification, are permitted provided that the following conditions are met:
 *     * Redistributions of source code must retain the above copyright notice,
 *this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above copyright
 *notice, this list of conditions and the following disclaimer in the
 *documentation and/or other materials provided with the distribution.
 *     * Neither the name of the NVIDIA CORPORATION nor the names of its
 *contributors may be used to endorse or promote products derived from this
 *software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 *AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 *IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 *DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE FOR ANY DIRECT,
 *INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 *OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TOR (INCLUDING
 *NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 *EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 **************************************************************************************************/

/*! \file
    \brief Template for a multistage GEMM kernel. Does not compute batching or
   support split-K.
*/

#pragma once

#include "cutlass/arch/arch.h"
#include "cutlass/cutlass.h"
#include "cutlass/gemm/threadblock/default_mma_core_sm80.h"
#include "cutlass/gemm/threadblock/default_mma.h"
#include "cutlass/gemm/threadblock/mma_planar_complex_multistage.h"

#include "cutlass/numeric_types.h"
#include "cutlass/transform/threadblock/predicated_tile_iterator.h"

////////////////////////////////////////////////////////////////////////////////

namespace cutlass {
namespace gemm {
namespace threadblock {

////////////////////////////////////////////////////////////////////////////////

template <
        /// Element type for A matrix operand
        typename ElementA_,
        /// Layout type for A matrix operand
        typename LayoutA_,
        /// Access granularity of A matrix in units of elements
        int kAlignmentA,
        /// Element type for B matrix operand
        typename ElementB_,
        /// Layout type for B matrix operand
        typename LayoutB_,
        /// Access granularity of B matrix in units of elements
        int kAlignmentB,
        /// Element type for internal accumulation
        typename ElementAccumulator_,
        /// Layout type for C and D matrix operands
        typename LayoutC_,
        /// Operator class tag
        typename OperatorClass_,
        /// Tag indicating architecture to tune for
        typename ArchTag_,
        /// Threadblock-level tile size (concept: GemmShape)
        typename ThreadblockShape_,
        /// Warp-level tile size (concept: GemmShape)
        typename WarpShape_,
        /// Instruction-level tile size (concept: GemmShape)
        typename InstructionShape_,
        /// Number of stages used in the pipelined mainloop
        int Stages,
        /// Complex transformation on operand A
        ComplexTransform TransformA = ComplexTransform::kNone,
        /// Complex transformation on operand B
        ComplexTransform TransformB = ComplexTransform::kNone,
        /// Math operator tag (e.g. arch::OpMultiplyAdd)
        typename Operator = arch::OpMultiplyAdd>
struct DefaultMmaPlanarComplexMultistage {
    // Construct a planar complex variant from the real-valued variant
    using RealMmaMultistage = typename DefaultMma<
            ElementA_, LayoutA_, kAlignmentA, ElementB_, LayoutB_, kAlignmentB,
            ElementAccumulator_, LayoutC_, OperatorClass_, ArchTag_,
            ThreadblockShape_, WarpShape_, InstructionShape_, Stages,
            Operator>::ThreadblockMma;

    using ThreadblockMma = MmaPlanarComplexMultistage<
            ThreadblockShape_, typename RealMmaMultistage::IteratorA,
            typename RealMmaMultistage::SmemIteratorA,
            cutlass::arch::CacheOperation::Global,
            typename RealMmaMultistage::IteratorB,
            typename RealMmaMultistage::SmemIteratorB,
            cutlass::arch::CacheOperation::Global, ElementAccumulator_,
            LayoutC_, typename RealMmaMultistage::Policy, Stages, TransformA,
            TransformB>;
};

////////////////////////////////////////////////////////////////////////////////

}  // namespace threadblock
}  // namespace gemm
}  // namespace cutlass

////////////////////////////////////////////////////////////////////////////////
