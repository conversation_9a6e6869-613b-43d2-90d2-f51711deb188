# YOLOv5 🚀 by Ultralytics, GPL-3.0 license

# Parameters
nc: 80  # number of classes
depth_multiple: 0.33  # model depth multiple
width_multiple: 0.25  # layer channel multiple
anchors:
  - [10,13, 16,30, 33,23]  # P3/8
  - [30,61, 62,45, 59,119]  # P4/16
  - [116,90, 156,198, 373,326]  # P5/32

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, Conv, [64, 6, 2, 2]],  # 0-P1/2
   [-1, 1, Conv, [128, 3, 2]],  # 1-P2/4
   [-1, 3, C3, [128]],
   [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
   [-1, 6, C3, [256]],
   [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
   [-1, 9, C3, [512]],
   [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
   [-1, 3, C3, [1024]],
   [-1, 1, <PERSON><PERSON><PERSON>, [1024, 5]],  # 9
  ]

# DAMO-<PERSON><PERSON><PERSON> GFPN Head
head:
  [[-1, 1, Conv, [512, 1, 1]], # 10
   [6, 1, Conv, [512, 3, 2]],
   [[-1, 10], 1, Concat, [1]],
   [-1, 3, CSPStage, [512]], # 13

   [-1, 1, nn.Upsample, [None, 2, 'nearest']], #14
   [4, 1, Conv, [256, 3, 2]], # 15
   [[14, -1, 6], 1, Concat, [1]],
   [-1, 3, CSPStage, [512]], # 17

   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[-1, 4], 1, Concat, [1]],
   [-1, 3, CSPStage, [256]], # 20

   [-1, 1, Conv, [256, 3, 2]],
   [[-1, 17], 1, Concat, [1]],
   [-1, 3, CSPStage, [512]], # 23

   [17, 1, Conv, [256, 3, 2]], # 24
   [23, 1, Conv, [256, 3, 2]], # 25
   [[13, 24, -1], 1, Concat, [1]],
   [-1, 3, CSPStage, [1024]], # 27

   [[20, 23, 27], 1, Detect, [nc]],  # Detect(P3, P4, P5)
  ]