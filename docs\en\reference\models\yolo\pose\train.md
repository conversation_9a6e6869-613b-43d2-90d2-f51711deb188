---
description: Explore the PoseTrainer class for training pose models using YOLO from Ultralytics. Includes initialization, model configuration, and plotting methods.
keywords: PoseTrainer, YOLO, Ultralytics, pose models, training, model configuration, deep learning, machine learning, pose estimation
---

# Reference for `ultralytics/models/yolo/pose/train.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/train.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/pose/train.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/pose/train.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.yolo.pose.train.PoseTrainer

<br><br>
