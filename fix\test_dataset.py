import warnings
warnings.filterwarnings('ignore')
from ultralytics import YOLO
from ultralytics.data import YOLODataset
import yaml

if __name__ == '__main__':
    try:
        # 读取数据集配置
        with open('ultralytics/cfg/datasets/fire-smoke-dataset.yaml', 'r', encoding='utf-8') as f:
            data_config = yaml.safe_load(f)
            
        print("📋 数据集配置:")
        print(f"  - 路径: {data_config['path']}")
        print(f"  - 训练集: {data_config['train']}")
        print(f"  - 验证集: {data_config['val']}")
        print(f"  - 类别数: {data_config['nc']}")
        print(f"  - 类别名: {data_config['names']}")
        
        # 检查路径是否存在
        import os
        full_train_path = os.path.join(data_config['path'], data_config['train'])
        full_val_path = os.path.join(data_config['path'], data_config['val'])
        
        print(f"\n🔍 检查路径:")
        print(f"  - 训练集路径: {full_train_path}")
        print(f"  - 存在: {os.path.exists(full_train_path)}")
        print(f"  - 验证集路径: {full_val_path}")
        print(f"  - 存在: {os.path.exists(full_val_path)}")
        
        if os.path.exists(full_train_path):
            train_images = os.listdir(full_train_path)
            print(f"  - 训练集图像数量: {len(train_images)}")
            print(f"  - 前5个文件: {train_images[:5]}")
            
        if os.path.exists(full_val_path):
            val_images = os.listdir(full_val_path)
            print(f"  - 验证集图像数量: {len(val_images)}")
            print(f"  - 前5个文件: {val_images[:5]}")
            
        # 检查标签文件
        train_labels_path = os.path.join(data_config['path'], 'train', 'labels')
        val_labels_path = os.path.join(data_config['path'], 'val', 'labels')
        
        print(f"\n🏷️ 检查标签:")
        print(f"  - 训练集标签路径: {train_labels_path}")
        print(f"  - 存在: {os.path.exists(train_labels_path)}")
        
        if os.path.exists(train_labels_path):
            train_labels = os.listdir(train_labels_path)
            print(f"  - 训练集标签数量: {len(train_labels)}")
            print(f"  - 前5个文件: {train_labels[:5]}")
            
        print(f"  - 验证集标签路径: {val_labels_path}")
        print(f"  - 存在: {os.path.exists(val_labels_path)}")
        
        if os.path.exists(val_labels_path):
            val_labels = os.listdir(val_labels_path)
            print(f"  - 验证集标签数量: {len(val_labels)}")
            print(f"  - 前5个文件: {val_labels[:5]}")
            
        print("\n✅ 数据集检查完成")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc() 