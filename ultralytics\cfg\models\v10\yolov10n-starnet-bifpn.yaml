# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]
fusion_mode: bifpn
node_mode: C2f
head_channel: 256

# 0-P1/2
# 1-P2/4
# 2-P3/8
# 3-P4/16
# 4-P5/32

backbone:
  # [from, repeats, module, args]
  - [-1, 1, starnet_s050, []]  # 4
  - [-1, 1, SPPF, [1024, 5]]  # 5
  - [-1, 1, PSA, [1024]] # 6

head:
  - [2, 1, Conv, [head_channel]]  # 7-P3/8
  - [3, 1, Conv, [head_channel]]  # 8-P4/16
  - [6, 1, Conv, [head_channel]]  # 9-P5/32

  - [-1, 1, nn.<PERSON>sample, [None, 2, 'nearest']] # 10 P5->P4
  - [[-1, 8], 1, Fusion, [fusion_mode]] # 11
  - [-1, 3, node_mode, [head_channel]] # 12-P4/16
  
  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 13 P4->P3
  - [[-1, 7], 1, Fusion, [fusion_mode]] # 14
  - [-1, 3, node_mode, [head_channel]] # 15-P3/8

  - [1, 1, Conv, [head_channel, 3, 2]] # 16 P2->P3
  - [[-1, 7, 15], 1, Fusion, [fusion_mode]] # 16
  - [-1, 3, node_mode, [head_channel]] # 17-P3/8

  - [-1, 1, Conv, [head_channel, 3, 2]] # 18 P3->P4
  - [[-1, 8, 12], 1, Fusion, [fusion_mode]] # 19
  - [-1, 3, node_mode, [head_channel]] # 20-P4/16

  - [-1, 1, Conv, [head_channel, 3, 2]] # 21 P4->P5
  - [[-1, 9], 1, Fusion, [fusion_mode]] # 22
  - [-1, 3, node_mode, [head_channel]] # 23-P5/32

  - [[17, 20, 23], 1, v10Detect, [nc]]  # Detect(P3, P4, P5)