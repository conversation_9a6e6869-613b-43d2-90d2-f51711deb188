/***************************************************************************************************
 * Copyright (c) 2017-2020, NVIDIA CORPORATION.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 *modification, are permitted provided that the following conditions are met:
 *     * Redistributions of source code must retain the above copyright notice,
 *this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above copyright
 *notice, this list of conditions and the following disclaimer in the
 *documentation and/or other materials provided with the distribution.
 *     * Neither the name of the NVIDIA CORPORATION nor the names of its
 *contributors may be used to endorse or promote products derived from this
 *software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 *AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 *IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 *DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE FOR ANY DIRECT,
 *INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 *OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TOR (INCLUDING
 *NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 *EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 **************************************************************************************************/
/*! \file
    \brief Templates implementing warp-level matrix multiply-accumulate
   operations.
*/

#pragma once

#include "cutlass/cutlass.h"
#include "cutlass/array.h"

/////////////////////////////////////////////////////////////////////////////////////////////////

namespace cutlass {

/////////////////////////////////////////////////////////////////////////////////////////////////

/// Array holding planar complex elements
template <typename Element_, int N>
struct ArrayPlanarComplex {
    /// Underlying real element
    using Element = Element_;

    /// Number of logical elements
    static size_t const kElements = N;

    /// Underlying Fragment of real-valued elemenets
    using ArrayReal = Array<Element, N>;

public:
    /// Fragment of real-valued elements representing the real part
    ArrayReal real;

    /// Fragment of real-valued elements representing the imaginary part
    ArrayReal imag;

public:
    /// Ctor
    CUTLASS_HOST_DEVICE
    ArrayPlanarComplex() {}

    /// Ctor
    CUTLASS_HOST_DEVICE
    ArrayPlanarComplex(ArrayReal const& real_, ArrayReal const& imag_)
            : real(real_), imag(imag_) {}

    /// Sets the array to zero efficiently
    CUTLASS_HOST_DEVICE
    void clear() {
        real.clear();
        imag.clear();
    }
};

/////////////////////////////////////////////////////////////////////////////////////////////////

/// Helper to deduce template arguments
template <typename Element, int N>
CUTLASS_HOST_DEVICE ArrayPlanarComplex<Element, N> make_ArrayPlanarComplex(
        Array<Element, N> const& real, Array<Element, N> const& imag) {
    return ArrayPlanarComplex<Element, N>(real, imag);
}

/////////////////////////////////////////////////////////////////////////////////////////////////

}  // namespace cutlass

/////////////////////////////////////////////////////////////////////////////////////////////////
