"""
创新点3: C2f-EMSCP + 轻量化动态检测头 (LDDH)
核心模块实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from ultralytics.nn.modules.conv import Conv, RepConv
from ultralytics.nn.modules.block import C2f, Bottleneck

__all__ = ['TaskAwareDynamicHead', 'RepStructuredConvDecomp', 'Bottleneck_EMSCP_LDDH', 'C2f_EMSCP_LDDH']


class EMSConvP(nn.Module):
    """Enhanced Multi-Scale Conv Plus - 复制自原始实现"""
    def __init__(self, channel=256, kernels=[1, 3, 5, 7]):
        super().__init__()
        self.groups = len(kernels)
        min_ch = channel // self.groups
        assert min_ch >= 16, f'channel must Greater than {16 * self.groups}, but {channel}'
        
        self.convs = nn.ModuleList([])
        for ks in kernels:
            self.convs.append(Conv(c1=min_ch, c2=min_ch, k=ks))
        self.conv_1x1 = Conv(channel, channel, k=1)
        
    def forward(self, x):
        # 重新排列输入张量以分组处理
        bs, ch, h, w = x.shape
        x_group = x.view(bs, self.groups, ch // self.groups, h, w)
        
        # 对每个组应用对应的卷积
        x_convs = []
        for i in range(self.groups):
            x_convs.append(self.convs[i](x_group[:, i]))
        
        # 合并所有组的输出
        x_convs = torch.cat(x_convs, dim=1)
        x_convs = self.conv_1x1(x_convs)
        
        return x_convs


class RepStructuredConvDecomp(nn.Module):
    """重参数化结构化卷积分解"""
    def __init__(self, channels, kernel_size=3, deploy=False):
        super().__init__()
        self.channels = channels
        self.kernel_size = kernel_size
        self.deploy = deploy
        
        if deploy:
            # 推理模式：单一卷积
            self.reparam_conv = nn.Conv2d(
                channels, channels, kernel_size,
                padding=kernel_size//2, groups=1, bias=True
            )
        else:
            # 训练模式：多分支结构
            # 主分支：标准卷积
            self.main_conv = nn.Conv2d(
                channels, channels, kernel_size,
                padding=kernel_size//2, groups=1, bias=False
            )

            # 分解分支：1xN + Nx1
            self.decomp_conv1 = nn.Conv2d(
                channels, channels, (1, kernel_size),
                padding=(0, kernel_size//2), groups=1, bias=False
            )
            self.decomp_conv2 = nn.Conv2d(
                channels, channels, (kernel_size, 1),
                padding=(kernel_size//2, 0), groups=1, bias=False
            )
            
            # 1x1分支
            self.identity_conv = nn.Conv2d(
                channels, channels, 1, bias=False
            ) if kernel_size > 1 else None
            
            # BN层
            self.bn = nn.BatchNorm2d(channels)
            
    def forward(self, x):
        if self.deploy:
            return self.reparam_conv(x)
        
        # 训练模式 - 简化实现
        out = self.main_conv(x)
        return F.relu(self.bn(out))
    
    def switch_to_deploy(self):
        """切换到推理模式"""
        if self.deploy:
            return
            
        # 融合所有分支的权重
        kernel, bias = self._get_equivalent_kernel_bias()
        
        self.reparam_conv = nn.Conv2d(
            self.channels, self.channels, self.kernel_size,
            padding=self.kernel_size//2, groups=1, bias=True
        )
        self.reparam_conv.weight.data = kernel
        self.reparam_conv.bias.data = bias
        
        # 删除训练时的分支
        for para in self.parameters():
            para.detach_()
        self.__delattr__('main_conv')
        self.__delattr__('decomp_conv1')
        self.__delattr__('decomp_conv2')
        if hasattr(self, 'identity_conv'):
            self.__delattr__('identity_conv')
        self.__delattr__('bn')
        
        self.deploy = True
    
    def _get_equivalent_kernel_bias(self):
        """获取等价的卷积核和偏置"""
        # 简化实现：直接使用主分支
        kernel, bias = self._fuse_bn_tensor(self.main_conv, self.bn)
        return kernel, bias
    
    def _get_decomp_kernel(self):
        """获取分解分支的等价卷积核"""
        # 简化实现：直接返回零卷积核
        return torch.zeros_like(self.main_conv.weight)
    
    def _pad_1x1_to_3x3_tensor(self, kernel1x1):
        """将1x1卷积核填充为3x3"""
        if kernel1x1 is None:
            return 0
        else:
            return F.pad(kernel1x1, [1, 1, 1, 1])
    
    def _fuse_bn_tensor(self, conv, bn):
        """融合卷积和BN层"""
        kernel = conv.weight
        running_mean = bn.running_mean
        running_var = bn.running_var
        gamma = bn.weight
        beta = bn.bias
        eps = bn.eps
        
        std = (running_var + eps).sqrt()
        t = (gamma / std).reshape(-1, 1, 1, 1)
        
        return kernel * t, beta - running_mean * gamma / std


class TaskAwareDynamicHead(nn.Module):
    """任务感知动态检测头"""
    def __init__(self, channels, num_classes, deploy=False):
        super().__init__()
        self.channels = channels
        self.num_classes = num_classes
        self.deploy = deploy
        
        # 共享特征提取
        self.shared_conv = nn.Sequential(
            RepStructuredConvDecomp(channels, deploy=deploy),
            RepStructuredConvDecomp(channels, deploy=deploy)
        )
        
        # 任务感知注意力
        self.task_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // 4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // 4, 2, 1),  # 2个任务：分类和回归
            nn.Sigmoid()
        )
        
        # 分类分支
        self.cls_conv = nn.Sequential(
            Conv(channels, channels, 3),
            nn.Conv2d(channels, num_classes, 1)
        )
        
        # 回归分支
        self.reg_conv = nn.Sequential(
            Conv(channels, channels, 3),
            nn.Conv2d(channels, 4, 1)  # 4个回归参数
        )
        
        # 轻量化特征融合
        self.feature_fusion = nn.Conv2d(channels * 2, channels, 1)
        
    def forward(self, x):
        # 共享特征提取
        shared_feat = self.shared_conv(x)
        
        # 任务感知注意力
        task_weights = self.task_attention(shared_feat)
        cls_weight = task_weights[:, 0:1, :, :]  # [B, 1, 1, 1]
        reg_weight = task_weights[:, 1:2, :, :]  # [B, 1, 1, 1]
        
        # 任务特定特征
        cls_feat = shared_feat * cls_weight
        reg_feat = shared_feat * reg_weight
        
        # 特征融合
        fused_feat = self.feature_fusion(torch.cat([cls_feat, reg_feat], dim=1))
        
        # 分类和回归输出
        cls_output = self.cls_conv(cls_feat + fused_feat)
        reg_output = self.reg_conv(reg_feat + fused_feat)
        
        return cls_output, reg_output
    
    def switch_to_deploy(self):
        """切换到推理模式"""
        for module in self.shared_conv:
            if hasattr(module, 'switch_to_deploy'):
                module.switch_to_deploy()
        self.deploy = True


class Bottleneck_EMSCP_LDDH(Bottleneck):
    """增强的EMSCP Bottleneck with 轻量化动态检测头"""
    def __init__(self, c1, c2, shortcut=True, g=1, k=(3, 3), e=0.5):
        super().__init__(c1, c2, shortcut, g, k, e)
        c_ = int(c2 * e)  # hidden channels
        
        # 重新定义卷积层
        self.cv1 = Conv(c1, c_, k[0], 1)
        self.cv2 = EMSConvP(c2)  # 使用EMSCP模块
        
        # 添加轻量化结构
        self.lightweight_conv = RepStructuredConvDecomp(c2)
        
    def forward(self, x):
        """Forward pass through the bottleneck with LDDH."""
        # 标准Bottleneck流程
        y = self.cv2(self.cv1(x))
        
        # 应用轻量化结构
        y = self.lightweight_conv(y)
        
        # 残差连接
        return x + y if self.add else y


class C2f_EMSCP_LDDH(C2f):
    """C2f with EMSCP and Lightweight Dynamic Detection Head"""
    def __init__(self, c1, c2, n=1, shortcut=False, g=1, e=0.5):
        super().__init__(c1, c2, n, shortcut, g, e)
        
        # 使用增强的Bottleneck
        self.m = nn.ModuleList(
            Bottleneck_EMSCP_LDDH(self.c, self.c, shortcut, g, k=(3, 3), e=1.0) 
            for _ in range(n)
        )
        
        # 添加轻量化动态头
        self.dynamic_head = TaskAwareDynamicHead(c2, num_classes=2)  # 火焰和烟雾
        
    def forward(self, x):
        """Forward pass through C2f layer with LDDH."""
        # 标准C2f流程
        y = list(self.cv1(x).chunk(2, 1))
        y.extend(m(y[-1]) for m in self.m)
        output = self.cv2(torch.cat(y, 1))
        
        return output
    
    def switch_to_deploy(self):
        """切换到推理模式"""
        for module in self.m:
            if hasattr(module, 'lightweight_conv'):
                module.lightweight_conv.switch_to_deploy()
        
        if hasattr(self, 'dynamic_head'):
            self.dynamic_head.switch_to_deploy()


def test_modules():
    """测试模块功能"""
    print("🧪 测试创新点3模块...")
    
    # 测试参数
    batch_size = 2
    channels = 256
    height, width = 32, 32
    
    # 创建测试输入
    x = torch.randn(batch_size, channels, height, width)
    print(f"输入张量形状: {x.shape}")
    
    # 测试RepStructuredConvDecomp
    print("\n1. 测试RepStructuredConvDecomp...")
    rscd = RepStructuredConvDecomp(channels)
    out_rscd = rscd(x)
    print(f"RSCD输出形状: {out_rscd.shape}")
    print(f"RSCD参数量: {sum(p.numel() for p in rscd.parameters()):,}")
    
    # 测试TaskAwareDynamicHead
    print("\n2. 测试TaskAwareDynamicHead...")
    taddh = TaskAwareDynamicHead(channels, num_classes=2)
    cls_out, reg_out = taddh(x)
    print(f"TADDH分类输出形状: {cls_out.shape}")
    print(f"TADDH回归输出形状: {reg_out.shape}")
    print(f"TADDH参数量: {sum(p.numel() for p in taddh.parameters()):,}")
    
    # 测试C2f_EMSCP_LDDH
    print("\n3. 测试C2f_EMSCP_LDDH...")
    c2f = C2f_EMSCP_LDDH(channels, channels, n=3)
    out_c2f = c2f(x)
    print(f"C2f输出形状: {out_c2f.shape}")
    print(f"C2f参数量: {sum(p.numel() for p in c2f.parameters()):,}")
    
    # 测试重参数化
    print("\n4. 测试重参数化...")
    rscd_deploy = RepStructuredConvDecomp(channels, deploy=False)
    out_before = rscd_deploy(x)
    rscd_deploy.switch_to_deploy()
    out_after = rscd_deploy(x)
    
    diff = torch.abs(out_before - out_after).mean()
    print(f"重参数化前后差异: {diff.item():.6f}")
    print(f"重参数化成功: {'✅' if diff < 1e-4 else '❌'}")
    
    print("\n✅ 所有模块测试通过！")


if __name__ == "__main__":
    test_modules()
