import warnings
warnings.filterwarnings('ignore')
from ultralytics import YOLO

if __name__ == '__main__':
    try:
        # 创建模型
        model = YOLO('ultralytics/cfg/models/v8/yolov8-C2f-DCNV2.yaml')
        
        print("✅ 模型加载成功")
        
        # 开始训练
        print("🚀 开始训练...")
        model.train(
            data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            epochs=1,  # 只训练1个epoch来测试
            imgsz=640,
            batch=1,   # 使用小batch size
            workers=0,
            device='0',
            verbose=True,
            project='runs/train',
            name='test-run',
        )
        print("✅ 训练完成")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc() 