"""
创新点5: C2f-EMSCP-AAAF + MultiSEAMHead
核心模块实现: 基于创新点1 + 多尺度空间-通道增强注意力检测头

结合Innovation1的最佳配置 + MultiSEAM检测头
目标: 超越Innovation1的mAP50=0.77564
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from ultralytics.nn.modules.conv import Conv, RepConv
from ultralytics.nn.modules.block import C2f, Bottleneck

__all__ = ['EMSConvP', 'AdaptiveAttentionFusion', 'Bottleneck_EMSCP_AAF', 'C2f_EMSCP_AAF', 'SEAMBlock', 'MultiScaleSEAM', 'MultiSEAMHead', 'Detect_MultiSEAM']


class EMSConvP(nn.Module):
    """Enhanced Multi-Scale Conv Plus - 增强多尺度卷积金字塔"""
    def __init__(self, channel=256, kernels=[1, 3, 5, 7]):
        super().__init__()
        self.channel = channel
        self.groups = len(kernels)

        # 确保通道数能被组数整除
        if channel % self.groups != 0:
            # 调整通道数到最近的可整除值
            channel = ((channel + self.groups - 1) // self.groups) * self.groups
            self.channel = channel

        self.group_ch = channel // self.groups
        assert self.group_ch >= 16, f'group channel must >= 16, but got {self.group_ch}'

        # 为每个组创建卷积
        self.convs = nn.ModuleList([])
        for ks in kernels:
            self.convs.append(Conv(self.group_ch, self.group_ch, ks))

        # 输入通道调整（如果需要）
        self.input_adjust = Conv(channel, self.channel, 1) if channel != self.channel else nn.Identity()

        # 最终1x1卷积 - 输出回原始通道数
        self.conv_1x1 = Conv(self.channel, channel, 1)
        self.original_channel = channel
        
    def forward(self, x):
        # 调整输入通道数（如果需要）
        x = self.input_adjust(x)

        # 分组处理
        bs, _, h, w = x.shape
        x_group = x.view(bs, self.groups, self.group_ch, h, w)

        # 对每个组应用对应的卷积
        x_convs = []
        for i in range(self.groups):
            x_convs.append(self.convs[i](x_group[:, i]))

        # 合并所有组的输出
        x_convs = torch.cat(x_convs, dim=1)
        x_convs = self.conv_1x1(x_convs)

        return x_convs


class AAAF(nn.Module):
    """Adaptive Aggregation Attention Fusion - 自适应聚合注意力融合"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.channels = channels
        
        # 通道注意力
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(2, 1, kernel_size=7, padding=3, bias=False),
            nn.Sigmoid()
        )
        
        # 自适应融合权重
        self.fusion_conv = nn.Conv2d(channels, channels, 1, bias=False)
        self.fusion_bn = nn.BatchNorm2d(channels)
        
    def forward(self, x):
        # 通道注意力
        ca = self.channel_attention(x)
        x_ca = x * ca
        
        # 空间注意力
        avg_out = torch.mean(x_ca, dim=1, keepdim=True)
        max_out, _ = torch.max(x_ca, dim=1, keepdim=True)
        sa_input = torch.cat([avg_out, max_out], dim=1)
        sa = self.spatial_attention(sa_input)
        x_sa = x_ca * sa
        
        # 自适应融合
        x_fused = self.fusion_conv(x_sa)
        x_fused = self.fusion_bn(x_fused)
        
        # 残差连接
        return x + x_fused


# 为了兼容性，添加别名
AdaptiveAttentionFusion = AAAF


class Bottleneck_EMSCP_AAF(Bottleneck):
    """Enhanced Bottleneck with EMSCP and Adaptive Attention Fusion"""
    def __init__(self, c1, c2, shortcut=True, g=1, k=(3, 3), e=0.5):
        super().__init__(c1, c2, shortcut, g, k, e)
        c_ = int(c2 * e)  # hidden channels

        # 确保c_至少为16且能被4整除（EMSConvP的要求）
        if c_ < 64:
            c_ = max(64, c2)

        # 使用EMSConvP替换第二个卷积
        self.cv2 = EMSConvP(c_)

        # EMSConvP输出通道数与c_相同，如果与c2不同需要调整
        if c_ != c2:
            self.channel_adjust = Conv(c_, c2, 1)
        else:
            self.channel_adjust = nn.Identity()

        # 添加自适应注意力融合
        self.aaf = AdaptiveAttentionFusion(c2)

        # 残差连接条件
        self.add = shortcut and c1 == c2

    def forward(self, x):
        """Forward pass through the bottleneck with AAF."""
        x1 = self.cv1(x)
        y = self.cv2(x1)

        # 通道调整（确保输出通道数正确）
        y = self.channel_adjust(y)

        # 应用自适应注意力融合
        y = self.aaf(y)

        # 残差连接
        return x + y if self.add else y


class C2f_EMSCP_AAF(nn.Module):
    """C2f with Enhanced Multi-Scale Conv Plus and AAAF"""
    def __init__(self, c1, c2, n=1, shortcut=False, g=1, e=0.5):
        super().__init__()
        # 确保所有参数都是有效的整数
        c1, c2, n = int(c1), int(c2), int(n)
        self.c = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, 2 * self.c, 1, 1)
        self.cv2 = Conv((2 + n) * self.c, c2, 1)  # optional act=FReLU(c2)

        # 使用增强的Bottleneck
        self.m = nn.ModuleList(
            Bottleneck_EMSCP_AAF(self.c, self.c, shortcut, g, k=(3, 3), e=1.0)
            for _ in range(n)
        )

        # 在最后添加全局自适应注意力融合
        self.global_aaf = AdaptiveAttentionFusion(c2)

    def forward(self, x):
        """Forward pass through C2f layer with AAF."""
        # 标准C2f流程
        y = list(self.cv1(x).chunk(2, 1))
        y.extend(m(y[-1]) for m in self.m)
        output = self.cv2(torch.cat(y, 1))

        # 应用全局自适应注意力融合
        output = self.global_aaf(output)

        return output


class SEAMBlock(nn.Module):
    """Spatial-channel Enhanced Attention Module Block"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        
        # 空间注意力分支
        self.spatial_branch = nn.Sequential(
            nn.Conv2d(channels, channels // 2, 1, bias=False),
            nn.BatchNorm2d(channels // 2),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // 2, channels // 2, 3, padding=1, groups=channels // 2, bias=False),
            nn.BatchNorm2d(channels // 2),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // 2, 1, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 通道注意力分支
        self.channel_branch = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 特征融合
        self.fusion_conv = nn.Conv2d(channels, channels, 1, bias=False)
        self.fusion_bn = nn.BatchNorm2d(channels)
        
    def forward(self, x):
        # 空间注意力
        spatial_att = self.spatial_branch(x)
        x_spatial = x * spatial_att
        
        # 通道注意力
        channel_att = self.channel_branch(x_spatial)
        x_channel = x_spatial * channel_att
        
        # 特征融合
        x_fused = self.fusion_conv(x_channel)
        x_fused = self.fusion_bn(x_fused)
        
        return x + x_fused


class MultiScaleSEAM(nn.Module):
    """Multi-Scale SEAM for different feature levels"""
    def __init__(self, channels, scales=[1, 2, 4]):
        super().__init__()
        self.scales = scales
        self.seam_blocks = nn.ModuleList([
            SEAMBlock(channels) for _ in scales
        ])
        
        # 多尺度特征融合
        self.scale_fusion = nn.Conv2d(channels * len(scales), channels, 1, bias=False)
        self.scale_bn = nn.BatchNorm2d(channels)
        
    def forward(self, x):
        multi_scale_features = []
        
        for i, scale in enumerate(self.scales):
            if scale == 1:
                # 原始尺度
                feat = self.seam_blocks[i](x)
            else:
                # 下采样到不同尺度
                h, w = x.shape[2], x.shape[3]
                scaled_x = F.adaptive_avg_pool2d(x, (h // scale, w // scale))
                scaled_feat = self.seam_blocks[i](scaled_x)
                # 上采样回原始尺度
                feat = F.interpolate(scaled_feat, size=(h, w), mode='bilinear', align_corners=False)
            
            multi_scale_features.append(feat)
        
        # 融合多尺度特征
        fused_feat = torch.cat(multi_scale_features, dim=1)
        fused_feat = self.scale_fusion(fused_feat)
        fused_feat = self.scale_bn(fused_feat)
        
        return x + fused_feat


class MultiSEAMHead(nn.Module):
    """Multi-Scale Spatial-channel Enhanced Attention Module Head"""
    def __init__(self, nc=80, ch=256, anchors=()):
        super().__init__()
        self.nc = nc  # number of classes
        self.nl = len(anchors) if anchors else 3  # number of detection layers
        self.na = len(anchors[0]) // 2 if anchors else 1  # number of anchors
        self.ch = ch  # unified channel dimension
        
        # 输入通道统一化
        self.input_convs = nn.ModuleList([
            nn.Conv2d(ch_in, ch, 1, bias=False) for ch_in in [256, 512, 1024]
        ])
        
        # 多尺度SEAM模块
        self.multi_seam_blocks = nn.ModuleList([
            MultiScaleSEAM(ch, scales=[1, 2, 4]) for _ in range(self.nl)
        ])
        
        # 特征增强卷积
        self.enhance_convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(ch, ch, 3, padding=1, bias=False),
                nn.BatchNorm2d(ch),
                nn.ReLU(inplace=True),
                nn.Conv2d(ch, ch, 3, padding=1, bias=False),
                nn.BatchNorm2d(ch),
                nn.ReLU(inplace=True)
            ) for _ in range(self.nl)
        ])
        
        # 分类头
        self.cls_convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(ch, ch // 2, 3, padding=1, bias=False),
                nn.BatchNorm2d(ch // 2),
                nn.ReLU(inplace=True),
                nn.Conv2d(ch // 2, self.na * self.nc, 1)
            ) for _ in range(self.nl)
        ])
        
        # 回归头
        self.reg_convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(ch, ch // 2, 3, padding=1, bias=False),
                nn.BatchNorm2d(ch // 2),
                nn.ReLU(inplace=True),
                nn.Conv2d(ch // 2, self.na * 4, 1)
            ) for _ in range(self.nl)
        ])
        
        # 置信度头
        self.obj_convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(ch, ch // 4, 3, padding=1, bias=False),
                nn.BatchNorm2d(ch // 4),
                nn.ReLU(inplace=True),
                nn.Conv2d(ch // 4, self.na, 1)
            ) for _ in range(self.nl)
        ])
        
    def forward(self, x):
        outputs = []
        
        for i, xi in enumerate(x):
            # 统一输入通道
            if i < len(self.input_convs):
                xi = self.input_convs[i](xi)
            
            # 多尺度SEAM增强
            enhanced_feat = self.multi_seam_blocks[i](xi)
            
            # 特征进一步增强
            enhanced_feat = self.enhance_convs[i](enhanced_feat)
            
            # 分类预测
            cls_pred = self.cls_convs[i](enhanced_feat)
            
            # 回归预测
            reg_pred = self.reg_convs[i](enhanced_feat)
            
            # 置信度预测
            obj_pred = self.obj_convs[i](enhanced_feat)
            
            # 合并输出
            output = torch.cat([reg_pred, obj_pred, cls_pred], dim=1)
            outputs.append(output)
            
        return outputs


# 为了兼容性，创建Detect_MultiSEAM类
class Detect_MultiSEAM(MultiSEAMHead):
    """MultiSEAM检测头的兼容性包装"""
    def __init__(self, nc=80):
        # 创建虚拟anchors用于初始化
        anchors = [[[10, 13], [16, 30], [33, 23]], 
                  [[30, 61], [62, 45], [59, 119]], 
                  [[116, 90], [156, 198], [373, 326]]]
        super().__init__(nc, ch=256, anchors=anchors)


if __name__ == "__main__":
    # 测试代码
    print("🎯 创新点5模块测试")
    
    # 测试EMSConvP
    emscp = EMSConvP(256)
    x = torch.randn(1, 256, 32, 32)
    out = emscp(x)
    print(f"✅ EMSConvP: {x.shape} -> {out.shape}")
    
    # 测试AAAF
    aaaf = AAAF(256)
    out = aaaf(x)
    print(f"✅ AAAF: {x.shape} -> {out.shape}")
    
    # 测试C2f_EMSCP_AAAF
    c2f = C2f_EMSCP_AAAF(256, 256, n=3)
    out = c2f(x)
    print(f"✅ C2f_EMSCP_AAAF: {x.shape} -> {out.shape}")
    
    # 测试MultiSEAMHead
    multiseam = Detect_MultiSEAM(nc=2)
    x_list = [torch.randn(1, 256, 80, 80), torch.randn(1, 512, 40, 40), torch.randn(1, 1024, 20, 20)]
    outputs = multiseam(x_list)
    print(f"✅ MultiSEAMHead: {[xi.shape for xi in x_list]} -> {[out.shape for out in outputs]}")
    
    print("🎉 创新点5模块测试完成！")
