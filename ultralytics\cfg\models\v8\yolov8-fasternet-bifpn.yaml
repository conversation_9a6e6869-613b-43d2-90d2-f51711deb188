# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv8 object detection model with P3-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n.yaml' will call yolov8.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]  # YOLOv8n summary: 225 layers,  3157200 parameters,  3157184 gradients,   8.9 GFLOPs
  s: [0.33, 0.50, 1024]  # YOLOv8s summary: 225 layers, 11166560 parameters, 11166544 gradients,  28.8 GFLOPs
  m: [0.67, 0.75, 768]   # YOLOv8m summary: 295 layers, 25902640 parameters, 25902624 gradients,  79.3 GFLOPs
  l: [1.00, 1.00, 512]   # YOLOv8l summary: 365 layers, 43691520 parameters, 43691504 gradients, 165.7 GFLOPs
  x: [1.00, 1.25, 512]   # YOLOv8x summary: 365 layers, 68229648 parameters, 68229632 gradients, 258.5 GFLOPs
fusion_mode: bifpn
node_mode: C2f
head_channel: 256

# 0-P1/2
# 1-P2/4
# 2-P3/8
# 3-P4/16
# 4-P5/32

# YOLOv8.0n backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, fasternet_t0, []]  # 4
  - [-1, 1, SPPF, [1024, 5]]  # 5

# YOLOv8.0n head
head:
  - [2, 1, Conv, [head_channel]]  # 6-P3/8
  - [3, 1, Conv, [head_channel]]  # 7-P4/16
  - [5, 1, Conv, [head_channel]]  # 8-P5/32

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 9 P5->P4
  - [[-1, 7], 1, Fusion, [fusion_mode]] # 10
  - [-1, 3, node_mode, [head_channel]] # 11-P4/16
  
  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 12 P4->P3
  - [[-1, 6], 1, Fusion, [fusion_mode]] # 13
  - [-1, 3, node_mode, [head_channel]] # 14-P3/8

  - [1, 1, Conv, [head_channel, 3, 2]] # 15 P2->P3
  - [[-1, 6, 14], 1, Fusion, [fusion_mode]] # 16
  - [-1, 3, node_mode, [head_channel]] # 17-P3/8

  - [-1, 1, Conv, [head_channel, 3, 2]] # 18 P3->P4
  - [[-1, 7, 11], 1, Fusion, [fusion_mode]] # 19
  - [-1, 3, node_mode, [head_channel]] # 20-P4/16

  - [-1, 1, Conv, [head_channel, 3, 2]] # 21 P4->P5
  - [[-1, 8], 1, Fusion, [fusion_mode]] # 22
  - [-1, 3, node_mode, [head_channel]] # 23-P5/32

  - [[17, 20, 23], 1, Detect, [nc]]  # Detect(P3, P4, P5)
