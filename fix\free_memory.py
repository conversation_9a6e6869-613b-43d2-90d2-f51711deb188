import gc
import torch
import psutil
import subprocess
import sys

def free_system_memory():
    """释放系统内存"""
    print("🧹 系统内存清理工具")
    print("=" * 40)
    
    # 显示清理前内存状态
    memory_before = psutil.virtual_memory()
    print(f"清理前内存使用: {memory_before.percent}%")
    print(f"可用内存: {memory_before.available / (1024**3):.1f}GB")
    
    # 1. Python垃圾回收
    print("\n1. 执行Python垃圾回收...")
    collected = gc.collect()
    print(f"回收对象数: {collected}")
    
    # 2. GPU缓存清理
    if torch.cuda.is_available():
        print("\n2. 清理GPU缓存...")
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        print("GPU缓存已清理")
    
    # 3. 系统内存压缩（Windows）
    print("\n3. 执行系统内存清理...")
    try:
        # Windows内存压缩
        subprocess.run(['powershell', '-Command', 
                       '[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers()'], 
                      capture_output=True)
        print("系统内存压缩完成")
    except:
        print("系统内存压缩失败（需要管理员权限）")
    
    # 显示清理后内存状态
    memory_after = psutil.virtual_memory()
    print(f"\n清理后内存使用: {memory_after.percent}%")
    print(f"可用内存: {memory_after.available / (1024**3):.1f}GB")
    
    freed_mb = (memory_after.available - memory_before.available) / (1024**2)
    print(f"释放内存: {freed_mb:.0f}MB")
    
    return memory_after.percent

if __name__ == "__main__":
    memory_usage = free_system_memory()
    
    if memory_usage < 75:
        print("\n✅ 内存使用正常，可以使用高性能训练配置")
        print("推荐运行: python train_speed_optimized.py")
    else:
        print("\n⚠️  内存使用仍然较高，建议使用内存优化配置")
        print("推荐运行: python speed_fix_immediate.py") 