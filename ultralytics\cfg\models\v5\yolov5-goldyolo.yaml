# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv5 object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/yolov5

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov5n.yaml' will call yolov5.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]
  s: [0.33, 0.50, 1024]
  m: [0.67, 0.75, 1024]
  l: [1.00, 1.00, 1024]
  x: [1.33, 1.25, 1024]

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, Conv, [64, 6, 2, 2]],  # 0-P1/2
   [-1, 1, Conv, [128, 3, 2]],  # 1-P2/4
   [-1, 3, C3, [128]],
   [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
   [-1, 6, C3, [256]],
   [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
   [-1, 9, C3, [512]],
   [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
   [-1, 3, C3, [1024]],
   [-1, 1, SPPF, [1024, 5]],  # 9
  ]

# YOLOv5 v6.0 head
head:
  [[[2, 4, 6, 9], 1, SimFusion_4in, []], # 10
   [-1, 1, IFM, [[64, 32]]], # 11
   
   [9, 1, Conv, [512, 1, 1]], # 12
   [[4, 6, -1], 1, SimFusion_3in, [512]], # 13
   [[-1, 11], 1, InjectionMultiSum_Auto_pool, [512, [64, 32], 0]], # 14
   [-1, 3, C3, [512, False]], # 15

   [6, 1, Conv, [256, 1, 1]], # 16
   [[2, 4, -1], 1, SimFusion_3in, [256]], # 17
   [[-1, 11], 1, InjectionMultiSum_Auto_pool, [256, [64, 32], 1]], # 18
   [-1, 3, C3, [256, False]], # 19

   [[19, 15, 9], 1, PyramidPoolAgg, [352, 2]], # 20
   [-1, 1, TopBasicLayer, [352, [64, 128]]], # 21

   [[19, 16], 1, AdvPoolFusion, []], # 22
   [[-1, 21], 1, InjectionMultiSum_Auto_pool, [256, [64, 128], 0]], # 23
   [-1, 3, C3, [512, False]], # 24

   [[-1, 12], 1, AdvPoolFusion, []], # 25
   [[-1, 21], 1, InjectionMultiSum_Auto_pool, [512, [64, 128], 1]], # 26
   [-1, 3, C3, [1024, False]], # 27

   [[19, 24, 27], 1, Detect, [nc]] # 28
  ]
