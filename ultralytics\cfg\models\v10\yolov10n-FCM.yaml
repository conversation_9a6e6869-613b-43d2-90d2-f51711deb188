# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 1, FCM_3, []]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 1, FCM_2, []]
  - [-1, 1, SCDown, [512, 3, 2]] # 5-P4/16
  - [-1, 1, FCM_1, []]
  - [-1, 1, Pzconv, []] # 7-P4/16
  - [-1, 1, FCM, []]
  - [-1, 1, <PERSON><PERSON><PERSON>, [512, 5]] # 9

# YOLOv10.0n head
head:
  - [-1, 1, nn.<PERSON><PERSON><PERSON>, [None, 2, "nearest"]]
  - [[-1, 4], 1, Concat, [1]] # cat backbone P3
  - [-1, 3, C2f, [256]] # 12

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 2], 1, Concat, [1]] # cat backbone P2
  - [-1, 3, C2f, [128]] # 15 (P2/4)

  - [-1, 1, Conv, [128, 3, 2]]
  - [[-1, 12], 1, Concat, [1]] # cat head P3
  - [-1, 3, C2f, [256]] # 18 (P3/8)

  - [-1, 1, SCDown, [256, 3, 2]]
  - [[-1, 9], 1, Concat, [1]] # cat head P4
  - [-1, 3, C2fCIB, [512, True, True]] # 21 (P4/16)

  - [[18, 21], 1, v10Detect, [nc]] # Detect(P3, P4)
