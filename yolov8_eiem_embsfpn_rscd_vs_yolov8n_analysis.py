import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams
import seaborn as sns

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False

def analyze_training_results():
    """
    分析EIEM-EMBSFPN-RSCD模型与YOLOv8n的训练结果
    """
    print("="*80)
    print("🔥 YOLOv8-EIEM-EMBSFPN-RSCD vs YOLOv8n 深度分析报告")
    print("="*80)
    
    # 读取训练结果
    try:
        eiem_results = pd.read_csv('runs/train/fire-smoke-dataset-yolov8-EIEM-EMBSFPN-RSCD-ultimate/results.csv')
        yolov8n_results = pd.read_csv('runs/train/fire-smoke-dataset-yolov8n/results.csv')
    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")
        return
    
    # 清理列名
    eiem_results.columns = eiem_results.columns.str.strip()
    yolov8n_results.columns = yolov8n_results.columns.str.strip()
    
    print(f"📊 EIEM-EMBSFPN-RSCD训练轮数: {len(eiem_results)}")
    print(f"📊 YOLOv8n训练轮数: {len(yolov8n_results)}")
    
    # 获取最终结果
    eiem_final = eiem_results.iloc[-1]
    yolov8n_final = yolov8n_results.iloc[-1]
    
    print("\n🎯 最终性能对比")
    print("-"*60)
    
    # 核心指标对比
    metrics = [
        ('mAP50', 'metrics/mAP50(B)'),
        ('mAP50-95', 'metrics/mAP50-95(B)'),
        ('Precision', 'metrics/precision(B)'),
        ('Recall', 'metrics/recall(B)')
    ]
    
    performance_data = {}
    
    for metric_name, metric_key in metrics:
        eiem_val = eiem_final[metric_key]
        yolov8n_val = yolov8n_final[metric_key]
        improvement = ((eiem_val - yolov8n_val) / yolov8n_val) * 100
        
        performance_data[metric_name] = {
            'EIEM_EMBSFPN_RSCD': eiem_val,
            'YOLOv8n': yolov8n_val,
            'Improvement': improvement
        }
        
        status = "📈" if improvement > 0 else "📉"
        print(f"{metric_name:12} | EIEM: {eiem_val:.4f} | YOLOv8n: {yolov8n_val:.4f} | {status} {improvement:+.2f}%")
    
    # 损失对比
    print("\n📉 最终损失对比")
    print("-"*60)
    
    loss_metrics = [
        ('Val Box Loss', 'val/box_loss'),
        ('Val Cls Loss', 'val/cls_loss'),
        ('Val DFL Loss', 'val/dfl_loss')
    ]
    
    for loss_name, loss_key in loss_metrics:
        eiem_loss = eiem_final[loss_key]
        yolov8n_loss = yolov8n_final[loss_key]
        change = ((eiem_loss - yolov8n_loss) / yolov8n_loss) * 100
        
        status = "✅" if change < 0 else "❌"
        print(f"{loss_name:14} | EIEM: {eiem_loss:.4f} | YOLOv8n: {yolov8n_loss:.4f} | {status} {change:+.2f}%")
    
    # 问题分析
    print("\n🔍 深度问题分析")
    print("-"*60)
    
    # 1. 性能下降分析
    main_metrics_performance = [
        performance_data['mAP50']['Improvement'],
        performance_data['mAP50-95']['Improvement'],
        performance_data['Precision']['Improvement'],
        performance_data['Recall']['Improvement']
    ]
    
    avg_performance_drop = np.mean(main_metrics_performance)
    print(f"平均性能变化: {avg_performance_drop:.2f}%")
    
    if avg_performance_drop < -5:
        print("❌ 严重性能下降 (>5%)")
    elif avg_performance_drop < 0:
        print("⚠️ 轻微性能下降")
    else:
        print("✅ 性能提升")
    
    # 2. 收敛分析
    print(f"\n📈 训练收敛性分析")
    print("-"*40)
    
    # 分析最后10轮的稳定性
    if len(eiem_results) >= 10:
        eiem_last10 = eiem_results.tail(10)['metrics/mAP50(B)']
        eiem_stability = eiem_last10.std()
        eiem_trend = eiem_last10.iloc[-1] - eiem_last10.iloc[0]
        
        print(f"EIEM模型最后10轮:")
        print(f"  mAP50标准差: {eiem_stability:.6f}")
        print(f"  趋势变化: {eiem_trend:+.4f}")
        
        if eiem_stability > 0.01:
            print("  ⚠️ 训练不稳定，建议调整学习率")
        else:
            print("  ✅ 训练相对稳定")
    
    # 3. 架构问题诊断
    print(f"\n🏗️ 架构问题诊断")
    print("-"*40)
    
    # 检查是否存在过拟合
    eiem_train_loss = eiem_final['train/box_loss'] + eiem_final['train/cls_loss'] + eiem_final['train/dfl_loss']
    eiem_val_loss = eiem_final['val/box_loss'] + eiem_final['val/cls_loss'] + eiem_final['val/dfl_loss']
    
    overfitting_ratio = eiem_val_loss / eiem_train_loss
    print(f"训练/验证损失比: {overfitting_ratio:.3f}")
    
    if overfitting_ratio > 2.0:
        print("❌ 严重过拟合")
    elif overfitting_ratio > 1.5:
        print("⚠️ 轻微过拟合")
    else:
        print("✅ 拟合良好")
    
    # 4. 具体问题分析
    print(f"\n🎯 具体问题识别")
    print("-"*40)
    
    issues = []
    
    # 检查各项指标
    if performance_data['mAP50']['Improvement'] < -10:
        issues.append("mAP50严重下降 - 检测精度问题")
    
    if performance_data['Precision']['Improvement'] < -10:
        issues.append("精度严重下降 - 误检过多")
    
    if performance_data['Recall']['Improvement'] < -10:
        issues.append("召回率严重下降 - 漏检过多")
    
    if eiem_final['val/cls_loss'] > yolov8n_final['val/cls_loss'] * 2:
        issues.append("分类损失过高 - 分类头问题")
    
    if eiem_final['val/box_loss'] > yolov8n_final['val/box_loss'] * 1.5:
        issues.append("定位损失过高 - 回归头问题")
    
    if issues:
        print("发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
    else:
        print("✅ 未发现明显问题")
    
    # 5. 模块分析
    print(f"\n🧩 模块分析")
    print("-"*40)
    
    print("C2f-EIEM模块:")
    if eiem_final['train/box_loss'] > 2.0:
        print("  ⚠️ 特征提取可能有问题")
    else:
        print("  ✅ 特征提取正常")
    
    print("EMBSFPN模块:")
    if eiem_final['val/box_loss'] > yolov8n_final['val/box_loss']:
        print("  ⚠️ 特征融合效果不佳")
    else:
        print("  ✅ 特征融合有效")
    
    print("RSCD检测头:")
    if eiem_final['val/cls_loss'] > yolov8n_final['val/cls_loss']:
        print("  ⚠️ 检测头性能下降")
    else:
        print("  ✅ 检测头工作正常")
    
    # 6. 改进建议
    print(f"\n💡 改进建议")
    print("-"*40)
    
    suggestions = []
    
    if overfitting_ratio > 1.5:
        suggestions.append("增加正则化: weight_decay=0.001-0.002")
        suggestions.append("减少模型复杂度或增加数据增强")
    
    if performance_data['mAP50']['Improvement'] < -5:
        suggestions.append("调整学习率: lr0=0.0005-0.0008")
        suggestions.append("延长预热期: warmup_epochs=10-15")
    
    if eiem_final['val/cls_loss'] > 10:
        suggestions.append("调整分类损失权重: cls=0.3-0.5")
    
    if eiem_final['val/box_loss'] > 2:
        suggestions.append("调整定位损失权重: box=7.5-8.5")
    
    suggestions.append("尝试渐进式训练: 先训练backbone，再加入neck和head")
    suggestions.append("检查数据集质量和标注准确性")
    
    if suggestions:
        print("建议改进措施:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion}")
    
    # 7. 可视化对比
    create_comparison_plots(eiem_results, yolov8n_results, performance_data)
    
    print(f"\n📊 分析报告已生成完成!")
    print("="*80)

def create_comparison_plots(eiem_results, yolov8n_results, performance_data):
    """创建对比图表"""
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('YOLOv8-EIEM-EMBSFPN-RSCD vs YOLOv8n 对比分析', fontsize=16, fontweight='bold')
    
    # 确保两个结果长度一致，取较短的长度
    min_epochs = min(len(eiem_results), len(yolov8n_results))
    eiem_plot = eiem_results.head(min_epochs)
    yolov8n_plot = yolov8n_results.head(min_epochs)
    
    # mAP50对比
    axes[0,0].plot(eiem_plot['epoch'], eiem_plot['metrics/mAP50(B)'], 
                   label='EIEM-EMBSFPN-RSCD', linewidth=2, color='#e74c3c')
    axes[0,0].plot(yolov8n_plot['epoch'], yolov8n_plot['metrics/mAP50(B)'], 
                   label='YOLOv8n', linewidth=2, color='#3498db')
    axes[0,0].set_title('mAP50 训练曲线对比', fontweight='bold')
    axes[0,0].set_xlabel('Epoch')
    axes[0,0].set_ylabel('mAP50')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # 损失对比
    axes[0,1].plot(eiem_plot['epoch'], eiem_plot['val/box_loss'], 
                   label='EIEM Val Loss', linewidth=2, color='#e74c3c')
    axes[0,1].plot(yolov8n_plot['epoch'], yolov8n_plot['val/box_loss'], 
                   label='YOLOv8n Val Loss', linewidth=2, color='#3498db')
    axes[0,1].set_title('验证损失对比', fontweight='bold')
    axes[0,1].set_xlabel('Epoch')
    axes[0,1].set_ylabel('Validation Loss')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # 性能柱状图
    metrics_names = list(performance_data.keys())
    eiem_values = [performance_data[m]['EIEM_EMBSFPN_RSCD'] for m in metrics_names]
    yolov8n_values = [performance_data[m]['YOLOv8n'] for m in metrics_names]
    
    x = np.arange(len(metrics_names))
    width = 0.35
    
    bars1 = axes[1,0].bar(x - width/2, eiem_values, width, label='EIEM-EMBSFPN-RSCD', color='#e74c3c', alpha=0.7)
    bars2 = axes[1,0].bar(x + width/2, yolov8n_values, width, label='YOLOv8n', color='#3498db', alpha=0.7)
    
    axes[1,0].set_title('最终性能指标对比', fontweight='bold')
    axes[1,0].set_xlabel('指标')
    axes[1,0].set_ylabel('数值')
    axes[1,0].set_xticks(x)
    axes[1,0].set_xticklabels(metrics_names)
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)
    
    # 在柱状图上添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            axes[1,0].annotate(f'{height:.3f}',
                              xy=(bar.get_x() + bar.get_width() / 2, height),
                              xytext=(0, 3),
                              textcoords="offset points",
                              ha='center', va='bottom', fontsize=8)
    
    # 改进百分比图
    improvements = [performance_data[m]['Improvement'] for m in metrics_names]
    colors = ['red' if x < 0 else 'green' for x in improvements]
    
    bars = axes[1,1].bar(metrics_names, improvements, color=colors, alpha=0.7)
    axes[1,1].set_title('性能变化百分比', fontweight='bold')
    axes[1,1].set_xlabel('指标')
    axes[1,1].set_ylabel('变化百分比 (%)')
    axes[1,1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
    axes[1,1].grid(True, alpha=0.3)
    
    # 在柱状图上添加数值标签
    for bar in bars:
        height = bar.get_height()
        axes[1,1].annotate(f'{height:.1f}%',
                          xy=(bar.get_x() + bar.get_width() / 2, height),
                          xytext=(0, 3 if height >= 0 else -15),
                          textcoords="offset points",
                          ha='center', va='bottom' if height >= 0 else 'top',
                          fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('yolov8_eiem_embsfpn_rscd_vs_yolov8n_analysis.png', dpi=300, bbox_inches='tight')
    print(f"📊 对比图表已保存: yolov8_eiem_embsfpn_rscd_vs_yolov8n_analysis.png")

if __name__ == "__main__":
    analyze_training_results() 