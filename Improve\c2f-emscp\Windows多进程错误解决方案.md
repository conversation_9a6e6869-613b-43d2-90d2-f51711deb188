# Windows多进程错误解决方案 🔧

## 问题现象
```
RuntimeError: Couldn't open shared file mapping: <0000023A914EEBC2>, error code: <1455>
```

## ✅ 立即解决方案

### 方法1: 使用修复版脚本（推荐）
```bash
# 使用已修复的训练脚本
python Improve/c2f-emscp/train_c2f_emscp_progressive_fixed.py
```

### 方法2: 修改现有脚本
在任何训练脚本的**开头**添加：

```python
import multiprocessing as mp
if __name__ == '__main__':
    mp.set_start_method('spawn', force=True)
```

在`model.train()`中设置：
```python
model.train(
    # ... 其他参数 ...
    workers=0,      # 🔑 关键：禁用多进程
    cache=False,    # 🔑 关键：关闭缓存
    # ... 其他参数 ...
)
```

## 🧪 验证修复
```bash
python Improve/c2f-emscp/test_windows_fix.py
```

## 📁 可用的修复版脚本

| 脚本 | 说明 | 用途 |
|------|------|------|
| `train_c2f_emscp_progressive_fixed.py` | 三阶段渐进式训练 | **推荐使用** |
| `train_c2f_emscp_optimized_fixed.py` | 完整优化训练 | 一次性训练 |
| `test_windows_fix.py` | 修复验证脚本 | 测试环境 |

## ⚡ 核心修复代码

```python
import warnings, os
import torch
import multiprocessing as mp
import platform
warnings.filterwarnings('ignore')

# Windows多进程修复 - 必须在导入YOLO之前
if __name__ == '__main__':
    mp.set_start_method('spawn', force=True)

from ultralytics import YOLO

# 智能设置函数
def get_windows_settings():
    return {
        'workers': 0,  # 禁用多进程
        'cache': False,  # 关闭缓存
        'amp': torch.cuda.is_available(),  # GPU时启用AMP
    }

# 使用示例
if __name__ == '__main__':
    mp.set_start_method('spawn', force=True)
    
    model = YOLO('your_model.yaml')
    settings = get_windows_settings()
    
    model.train(
        data='your_data.yaml',
        workers=settings['workers'],
        cache=settings['cache'],
        amp=settings['amp'],
        # ... 其他参数
    )
```

## 🎯 下一步操作

1. **立即使用**：
   ```bash
   python Improve/c2f-emscp/train_c2f_emscp_progressive_fixed.py
   ```

2. **如果还有问题**：
   - 重启Python环境
   - 检查CUDA环境
   - 使用CPU训练测试：`device='cpu'`

## 💡 性能补偿

由于禁用了多进程，建议：
- 使用SSD硬盘
- 适当增加batch size
- 启用混合精度训练（GPU）

---

**总结**: 主要通过`workers=0`和`cache=False`解决，配合`mp.set_start_method('spawn')`确保兼容性。修复版脚本已包含所有必要设置。 