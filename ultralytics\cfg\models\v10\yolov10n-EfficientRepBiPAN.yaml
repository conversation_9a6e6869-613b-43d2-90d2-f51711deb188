# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, SCDown, [512, 3, 2]] # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, SCDown, [1024, 3, 2]] # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SPP<PERSON>, [1024, 5]] # 9
  - [-1, 1, PSA, [1024]] # 10

# YOLOv10.0n head
head:
  - [-1, 1, Conv, [256]] # 11
  - [[-1, 6, 4], 1, BiFusion, [256]] # 12
  - [-1, 12, RepBlock, [256]] # 13

  - [-1, 1, Conv, [128]] # 14
  - [[-1, 4, 2], 1, BiFusion, [128]] # 15
  - [-1, 12, RepBlock, [128]] # 16

  - [-1, 1, Conv, [128, 3, 2]] # 17
  - [[-1, 14], 1, Concat, [1]] # 18
  - [-1, 12, RepBlock, [256]] # 19

  - [-1, 1, Conv, [256, 3, 2]] # 20
  - [[-1, 11], 1, Concat, [1]] # 21
  - [-1, 12, RepBlock, [512]] # 22

  - [[16, 19, 22], 1, Detect, [nc]]  # Detect(P3, P4, P5)