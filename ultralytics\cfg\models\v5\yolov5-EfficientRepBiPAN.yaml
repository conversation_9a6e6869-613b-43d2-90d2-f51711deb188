# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv5 object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/yolov5

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov5n.yaml' will call yolov5.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]
  s: [0.33, 0.50, 1024]
  m: [0.67, 0.75, 1024]
  l: [1.00, 1.00, 1024]
  x: [1.33, 1.25, 1024]

# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, Conv, [64, 6, 2, 2]],  # 0-P1/2
   [-1, 1, Conv, [128, 3, 2]],  # 1-P2/4
   [-1, 3, C3, [128]],
   [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
   [-1, 6, C3, [256]],
   [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
   [-1, 9, C3, [512]],
   [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
   [-1, 3, C3, [1024]],
   [-1, 1, SPPF, [1024, 5]],  # 9
  ]

# YOLOv5 v6.0 head
head:
  [[-1, 1, Conv, [256]], # 10
  [[-1, 6, 4], 1, BiFusion, [256]], # 11
  [-1, 12, RepBlock, [256]], # 12

  [-1, 1, Conv, [128]], # 13
  [[-1, 4, 2], 1, BiFusion, [128]], # 14
  [-1, 12, RepBlock, [128]], # 15

  [-1, 1, Conv, [128, 3, 2]], # 16
  [[-1, 13], 1, Concat, [1]], # 17
  [-1, 12, RepBlock, [256]], # 18

  [-1, 1, Conv, [256, 3, 2]], # 19
  [[-1, 10], 1, Concat, [1]], # 20
  [-1, 12, RepBlock, [512]], # 21

  [[15, 18, 21], 1, Detect, [nc]],  # Detect(P3, P4, P5)
  ]