# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C2f, [128, True]]

  - [-1, 1, MutilScaleEdgeInfoGenetator, [[128, 256, 512]]] # 3
  - [3, 1, GetIndexOutput, [0]] # 4-P3/8
  - [3, 1, GetIndexOutput, [1]] # 5-P4/16
  - [3, 1, GetIndexOutput, [2]] # 6-P5/32

  - [2, 1, Conv, [256, 3, 2]] # 7-P3/8
  - [-1, 6, C2f, [256, True]]
  - [[4, -1], 1, Conv<PERSON><PERSON><PERSON><PERSON>, [256]] # 9-P3/8
  - [-1, 1, SCDown, [512, 3, 2]] # 10-P4/16
  - [-1, 6, C2f, [512, True]]
  - [[5, -1], 1, ConvEdgeFusion, [512]] # 12-P4/16
  - [-1, 1, SCDown, [1024, 3, 2]] # 13-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [[6, -1], 1, ConvEdgeFusion, [1024]] # 15-P5/32
  - [-1, 1, SPPF, [1024, 5]] # 16
  - [-1, 1, PSA, [1024]] # 17

# YOLOv10.0n head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 12], 1, Concat, [1]] # cat backbone P4
  - [-1, 3, C2f, [512]] # 20

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 9], 1, Concat, [1]] # cat backbone P3
  - [-1, 3, C2f, [256]] # 23 (P3/8-small)
  - [[4, -1], 1, ConvEdgeFusion, [256]] # 24-P3/8

  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 20], 1, Concat, [1]] # cat head P4
  - [-1, 3, C2f, [512]] # 27 (P4/16-medium)
  - [[5, -1], 1, ConvEdgeFusion, [512]] # 28-P4/16

  - [-1, 1, SCDown, [512, 3, 2]]
  - [[-1, 17], 1, Concat, [1]] # cat head P5
  - [-1, 3, C2fCIB, [1024, True, True]] # 31 (P5/32-large)
  - [[6, -1], 1, ConvEdgeFusion, [1024]] # 32-P5/32

  - [[30, 31, 32], 1, v10Detect, [nc]] # Detect(P3, P4, P5)
