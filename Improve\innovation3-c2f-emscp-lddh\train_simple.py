"""
创新点3简单训练脚本
C2f-EMSCP + 轻量化动态检测头 (LDDH)
"""

import sys
import os
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

import torch
from ultralytics import YOLO

# 注册自定义模块
from modules import TaskAwareDynamicHead, RepStructuredConvDecomp, Bottleneck_EMSCP_LDDH, C2f_EMSCP_LDDH

# 将模块添加到ultralytics命名空间
import ultralytics.nn.extra_modules.block as block_module
block_module.TaskAwareDynamicHead = TaskAwareDynamicHead
block_module.RepStructuredConvDecomp = RepStructuredConvDecomp
block_module.Bottleneck_EMSCP_LDDH = Bottleneck_EMSCP_LDDH
block_module.C2f_EMSCP_LDDH = C2f_EMSCP_LDDH

# 更新__all__列表
if hasattr(block_module, '__all__'):
    block_module.__all__.extend(['TaskAwareDynamicHead', 'RepStructuredConvDecomp', 'Bottleneck_EMSCP_LDDH', 'C2f_EMSCP_LDDH'])


def main():
    """主训练函数"""
    print("🚀 创新点3: C2f-EMSCP + 轻量化动态检测头 (LDDH) 训练")
    print("=" * 60)
    
    try:
        # 创建模型
        print("📦 加载模型配置...")
        model = YOLO('config.yaml')
        
        print("✅ 模型加载成功")
        print(f"📊 模型信息:")
        print(f"   - 参数量: {sum(p.numel() for p in model.model.parameters()):,}")
        print(f"   - 可训练参数: {sum(p.numel() for p in model.model.parameters() if p.requires_grad):,}")
        
        # 简单训练配置
        train_args = {
            'data': 'ultralytics/cfg/datasets/fire-smoke-dataset.yaml',  # 使用您的火焰烟雾数据集
            'epochs': 10,          # 10轮训练测试
            'imgsz': 640,
            'batch': 16,           # 适中批次
            'workers': 4,          # 多进程加速
            'device': 'cpu',       # 使用CPU
            'verbose': True,
            'save': True,          # 保存检查点
            'plots': True,         # 生成图表
            'val': True,           # 进行验证
            'project': 'runs/train',
            'name': 'innovation3-c2f-emscp-lddh',
            'exist_ok': True,
        }
        
        print("\n🎯 开始训练测试...")
        print("注意：这只是功能性测试，使用小数据集和少量epoch")
        
        # 开始训练
        results = model.train(**train_args)
        
        print("\n✅ 训练测试完成！")
        print("🎉 创新点3网络可以正常训练")
        
        # 测试重参数化切换
        print("\n🔄 测试重参数化切换...")
        for module in model.model.modules():
            if hasattr(module, 'switch_to_deploy'):
                module.switch_to_deploy()
                print("   ✅ 模块切换到推理模式")
        
        print("🚀 重参数化切换完成")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎊 创新点3训练脚本测试成功！")
        print("💡 提示：实际训练时请使用合适的数据集、更多epoch和GPU")
        print("🔧 提示：训练完成后可调用switch_to_deploy()切换到推理模式")
    else:
        print("\n⚠️ 训练脚本测试失败，请检查配置")
    
    sys.exit(0 if success else 1)
