import warnings, os
import torch
import torch.nn as nn
import multiprocessing as mp
warnings.filterwarnings('ignore')
from ultralytics import YOLO

# 三模块完整集成训练脚本：C2f-EIEM + EMBSFPN + RSCD
# 基于成功的BiFPN训练经验，针对三模块组合优化

if __name__ == '__main__':
    # Windows兼容性设置
    try:
        mp.set_start_method('spawn')
    except RuntimeError:
        pass
    
    print("=" * 80)
    print("🚀 C2f-EIEM + EMBSFPN + RSCD 三模块完整集成训练")
    print("=" * 80)
    print("📋 模块组合说明:")
    print("   🔧 主干网络: C2f-EIEM (边缘信息增强)")
    print("   🔧 颈部网络: EMBSFPN (高效多分支特征金字塔)")
    print("   🔧 检测头: RSCD (重参数共享卷积检测头)")
    print("=" * 80)
    
    # 1. 模型初始化
    model_config = 'ultralytics/cfg/models/v8/yolov8-EIEM-EMBSFPN-RSCD-ultimate.yaml'
    print(f"📝 加载模型配置: {model_config}")
    model = YOLO(model_config)
    
    # 2. 加载预训练权重
    print("📦 加载预训练权重...")
    model.load('yolov8n.pt')
    
    # 3. 三模块集成优化训练参数
    print("🎯 开始三模块集成优化训练...")
    model.train(
        # === 数据集配置 ===
        data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
        
        # === 基本训练参数 ===
        epochs=120,              # 增加训练轮数，复杂模型需要更长训练
        patience=70,             # 增加耐心值，三模块融合需要更多稳定时间
        batch=-1,                # 自动选择batch size
        imgsz=640,
        
        # === 设备设置 ===
        device="0" if os.environ.get('CUDA_VISIBLE_DEVICES') != '-1' else "cpu",
        workers=0,               # Windows兼容性
        
        # === 项目设置 ===
        project='runs/train',
        name='EIEM-EMBSFPN-RSCD-ultimate-combo',
        exist_ok=True,
        
        # === 模型设置 ===
        pretrained=False,        # 不使用预训练权重
        
        # === 优化器设置 ===
        optimizer="AdamW",
        lr0=0.0008,             # 降低初始学习率 (三模块复杂度高)
        lrf=0.00005,            # 更低的最终学习率
        momentum=0.937,
        weight_decay=0.001,     # 增加权重衰减，防止过拟合
        
        # === 学习率调度 ===
        cos_lr=True,            # 余弦学习率调度
        warmup_epochs=8.0,      # 增加预热轮数 (复杂模型需要更多预热)
        warmup_momentum=0.8,
        warmup_bias_lr=0.005,   # 降低预热偏置学习率
        
        # === 损失权重优化 - 三模块平衡 ===
        box=8.0,                # 增加框损失权重 (RSCD检测头优化)
        cls=0.6,                # 适中的分类损失权重
        dfl=1.8,                # 增加DFL损失权重 (EMBSFPN特征优化)
        
        # === 数据增强优化 - 边缘信息增强友好 ===
        hsv_h=0.012,            # 轻微色调变化，保护边缘信息
        hsv_s=0.6,              # 适度饱和度变化
        hsv_v=0.35,             # 适度亮度变化
        degrees=8.0,            # 轻微旋转，保护边缘特征
        translate=0.08,         # 轻微平移
        scale=0.5,              # 适度缩放范围
        shear=0.0,              # 不使用剪切 (保护边缘)
        perspective=0.0,        # 不使用透视变换 (保护边缘)
        flipud=0.0,             # 不使用上下翻转
        fliplr=0.5,             # 保持左右翻转
        mosaic=0.5,             # 适度马赛克概率
        mixup=0.1,              # 轻微mixup增强
        copy_paste=0.1,         # 轻微copy_paste增强
        
        # === 特殊的三模块优化设置 ===
        close_mosaic=20,        # 较早关闭马赛克，让模块融合稳定
        
        # === 验证和保存设置 ===
        val=True,
        save=True,
        save_period=10,         # 每10轮保存一次
        
        # === 可视化设置 ===
        plots=True,
        
        # === 其他优化设置 ===
        verbose=True,
        seed=42,
        deterministic=False,    # 允许一定随机性，提升泛化能力
        single_cls=False,       # 多类别检测
        rect=False,             # 不使用矩形训练
        resume=False,           # 不恢复训练
        nosave=False,           # 保存模型
        noval=False,            # 进行验证
        noautoanchor=False,     # 使用自动锚框
        noplots=False,          # 生成图表
        evolve=None,            # 不使用进化算法
        bucket='',              # 不使用云存储
        cache=False,            # Windows兼容性
        image_weights=False,    # 不使用图像权重
        device='',              # 使用自动设备选择
        multi_scale=False,      # 不使用多尺度训练 (三模块已足够复杂)
        overlap_mask=True,      # 允许掩码重叠
        mask_ratio=4,           # 掩码比例
        dropout=0.0,            # 不使用dropout
        val_iou=0.6,            # 验证IoU阈值
        val_conf=0.001,         # 验证置信度阈值
        val_max_det=300,        # 验证最大检测数
        
        # === 高级训练技巧 ===
        amp=True,               # 使用混合精度 (加速训练)
        fraction=1.0,           # 使用全部数据
        profile=False,          # 不启用性能分析
        freeze=None,            # 不冻结层
        
        # === 特定于三模块的优化 ===
        # C2f-EIEM: 边缘信息保护
        # EMBSFPN: 多尺度特征融合优化
        # RSCD: 轻量化检测头加速
        
        # 学习率调度器优化
        scheduler='cos',        # 使用余弦退火
        
        # 正则化优化
        fl_gamma=0.0,           # 不使用Focal Loss
        
        # 内存和速度优化
        half=False,             # 不使用半精度（保持稳定性）
        dnn=False,              # 不使用OpenCV DNN
        
        # 模型融合优化
        fuse=True,              # 启用模型融合
        
        # 最终验证设置
        rect_val=False,         # 验证时不使用矩形
        save_txt=False,         # 不保存txt文件
        save_conf=False,        # 不保存置信度
        save_json=True,         # 保存JSON结果
        save_hybrid=False,      # 不保存混合格式
        conf=None,              # 使用默认置信度
        iou=0.6,                # NMS IoU阈值
        max_det=300,            # 最大检测数
        half=False,             # 不使用半精度
        dnn=False,              # 不使用DNN
        plots=True,             # 生成图表
        source=None,            # 不指定源
        show=False,             # 不显示结果
        save_txt=False,         # 不保存txt
        save_conf=False,        # 不保存置信度
        save_crop=False,        # 不保存裁剪
        show_labels=True,       # 显示标签
        show_conf=True,         # 显示置信度
        vid_stride=1,           # 视频步长
        stream_buffer=False,    # 不使用流缓冲
        line_width=None,        # 使用默认线宽
        visualize=False,        # 不可视化特征
        augment=False,          # 推理时不增强
        agnostic_nms=False,     # 不使用类别无关NMS
        classes=None,           # 检测所有类别
        retina_masks=False,     # 不使用retina掩码
        embed=None,             # 不使用嵌入
        show_boxes=True,        # 显示边界框
        save_frames=False,      # 不保存帧
        
        # 任务和模式设置
        task='detect',          # 检测任务
        mode='train',           # 训练模式
    )
    
    print("=" * 80)
    print("🎉 三模块集成训练完成!")
    print("📊 模型保存路径: runs/train/EIEM-EMBSFPN-RSCD-ultimate-combo/")
    print("🔍 训练日志和图表已生成")
    print("💡 下一步: 使用测试脚本验证模型性能")
    print("=" * 80) 