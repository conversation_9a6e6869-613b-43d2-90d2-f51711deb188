---
description: Learn how to utilize the ultralytics.data.split_dota module to process and split DOTA datasets efficiently. Explore detailed functions and examples.
keywords: Ultralytics, DOTA dataset, data splitting, YOLO, Python, bbox_iof, load_yolo_dota, get_windows, crop_and_save
---

# Reference for `ultralytics/data/split_dota.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/split_dota.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/split_dota.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/split_dota.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.data.split_dota.bbox_iof

<br><br>

## ::: ultralytics.data.split_dota.load_yolo_dota

<br><br>

## ::: ultralytics.data.split_dota.get_windows

<br><br>

## ::: ultralytics.data.split_dota.get_window_obj

<br><br>

## ::: ultralytics.data.split_dota.crop_and_save

<br><br>

## ::: ultralytics.data.split_dota.split_images_and_labels

<br><br>

## ::: ultralytics.data.split_dota.split_trainval

<br><br>

## ::: ultralytics.data.split_dota.split_test

<br><br>
