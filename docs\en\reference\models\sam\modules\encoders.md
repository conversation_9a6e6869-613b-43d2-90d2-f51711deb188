---
description: Explore detailed documentation of various SAM encoder modules such as ImageEncoderViT, PromptEncoder, and more, available in Ultralytics' repository.
keywords: Ultralytics, SAM encoder, ImageEncoderViT, PromptEncoder, PositionEmbeddingRandom, Block, Attention, PatchEmbed
---

# Reference for `ultralytics/models/sam/modules/encoders.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/encoders.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/encoders.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/modules/encoders.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.sam.modules.encoders.ImageEncoderViT

<br><br>

## ::: ultralytics.models.sam.modules.encoders.PromptEncoder

<br><br>

## ::: ultralytics.models.sam.modules.encoders.PositionEmbeddingRandom

<br><br>

## ::: ultralytics.models.sam.modules.encoders.Block

<br><br>

## ::: ultralytics.models.sam.modules.encoders.Attention

<br><br>

## ::: ultralytics.models.sam.modules.encoders.PatchEmbed

<br><br>

## ::: ultralytics.models.sam.modules.encoders.window_partition

<br><br>

## ::: ultralytics.models.sam.modules.encoders.window_unpartition

<br><br>

## ::: ultralytics.models.sam.modules.encoders.get_rel_pos

<br><br>

## ::: ultralytics.models.sam.modules.encoders.add_decomposed_rel_pos

<br><br>
