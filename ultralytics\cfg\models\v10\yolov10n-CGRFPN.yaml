# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, SCDown, [512, 3, 2]] # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, SCDown, [1024, 3, 2]] # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SP<PERSON><PERSON>, [1024, 5]] # 9
  - [-1, 1, PSA, [1024]] # 10

# YOLOv10.0n head
head:
  - [[4, 6, 10], 1, PyramidContextExtraction, []] # 11
  - [11, 1, GetIndexOutput, [0]] # 12-P3/8
  - [11, 1, GetIndexOutput, [1]] # 13-P4/16
  - [11, 1, GetIndexOutput, [2]] # 14-P5/32

  - [10, 1, RCM, []] # 15-P5/32
  - [[-1, 14], 1, FuseBlockMulti, []] # 16-P5/32

  - [6, 1, RCM, []] # 17-P4/16
  - [[-1, 16], 1, DynamicInterpolationFusion, []] # 18-P4/16
  - [[-1, 13], 1, FuseBlockMulti, []] # 19-P4/16

  - [4, 1, RCM, []] # 20-P3/8
  - [[-1, 19], 1, DynamicInterpolationFusion, []] # 21-P3/8
  - [[-1, 12], 1, FuseBlockMulti, []] # 22-P3/8

  - [-1, 1, Conv, [256, 3, 2]] # 23-P4/16
  - [[-1, 19], 1, Concat, [1]] # 24-P4/16
  - [-1, 3, C2f, [512]] # 25-P4/16

  - [-1, 1, SCDown, [512, 3, 2]] # 26-P5/32
  - [[-1, 16], 1, Concat, [1]] # 27-P5/32
  - [-1, 3, C2fCIB, [1024, True, True]] # 28-P5/32

  - [[22, 25, 28], 1, v10Detect, [nc]] # Detect(P3, P4, P5)