import torch
import psutil
import platform
import time
import os
from pathlib import Path

def diagnose_training_speed():
    """诊断训练速度瓶颈"""
    print("🔍 训练速度诊断报告")
    print("=" * 50)
    
    # 1. 系统信息
    print("\n📊 系统配置:")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"CPU核心数: {os.cpu_count()}")
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total / (1024**3):.1f}GB")
    print(f"可用内存: {memory.available / (1024**3):.1f}GB")
    print(f"内存使用率: {memory.percent}%")
    
    # GPU信息
    if torch.cuda.is_available():
        print(f"\n🎮 GPU配置:")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"显存总量: {torch.cuda.get_device_properties(i).total_memory / (1024**3):.1f}GB")
            if torch.cuda.is_initialized():
                print(f"显存已用: {torch.cuda.memory_allocated(i) / (1024**3):.1f}GB")
                print(f"显存缓存: {torch.cuda.memory_reserved(i) / (1024**3):.1f}GB")
    else:
        print("\n❌ 未检测到GPU")
    
    # 2. 磁盘性能
    print(f"\n💾 磁盘性能:")
    disk = psutil.disk_usage('.')
    print(f"磁盘总量: {disk.total / (1024**3):.1f}GB")
    print(f"磁盘可用: {disk.free / (1024**3):.1f}GB")
    print(f"磁盘使用率: {(disk.used / disk.total) * 100:.1f}%")
    
    # 测试磁盘读写速度
    test_file = "speed_test.tmp"
    test_data = b"0" * (10 * 1024 * 1024)  # 10MB
    
    # 写入测试
    start_time = time.time()
    with open(test_file, 'wb') as f:
        for _ in range(10):  # 写入100MB
            f.write(test_data)
    write_time = time.time() - start_time
    write_speed = 100 / write_time
    
    # 读取测试
    start_time = time.time()
    with open(test_file, 'rb') as f:
        while f.read(1024 * 1024):  # 每次读1MB
            pass
    read_time = time.time() - start_time
    read_speed = 100 / read_time
    
    os.remove(test_file)
    
    print(f"磁盘写入速度: {write_speed:.1f}MB/s")
    print(f"磁盘读取速度: {read_speed:.1f}MB/s")
    
    # 3. 数据集分析
    print(f"\n📂 数据集分析:")
    dataset_path = Path("ultralytics/cfg/datasets/fire-smoke-dataset.yaml")
    if dataset_path.exists():
        print("✅ 数据集配置文件存在")
        
        # 检查图像目录大小
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'train:' in content:
                    print("✅ 训练集路径配置正确")
                else:
                    print("❌ 训练集路径配置可能有问题")
        except Exception as e:
            print(f"❌ 数据集配置读取错误: {e}")
    else:
        print("❌ 数据集配置文件不存在")
    
    # 4. 训练历史分析
    print(f"\n📈 训练历史分析:")
    runs_dir = Path("runs/train")
    if runs_dir.exists():
        recent_runs = sorted([d for d in runs_dir.iterdir() if d.is_dir()], 
                           key=lambda x: x.stat().st_mtime, reverse=True)[:5]
        
        for i, run_dir in enumerate(recent_runs):
            print(f"{i+1}. {run_dir.name}")
            
            # 检查训练配置
            args_file = run_dir / "args.yaml"
            if args_file.exists():
                with open(args_file, 'r') as f:
                    content = f.read()
                    workers = "workers: ?" 
                    cache = "cache: ?"
                    batch = "batch: ?"
                    
                    for line in content.split('\n'):
                        if 'workers:' in line:
                            workers = line.strip()
                        elif 'cache:' in line:
                            cache = line.strip()
                        elif 'batch:' in line:
                            batch = line.strip()
                    
                    print(f"   {workers}, {cache}, {batch}")
    
    # 5. 速度瓶颈分析
    print(f"\n🚨 速度瓶颈分析:")
    bottlenecks = []
    
    if memory.percent > 80:
        bottlenecks.append("内存使用率过高")
    
    if write_speed < 50:
        bottlenecks.append("磁盘写入速度慢")
    
    if read_speed < 100:
        bottlenecks.append("磁盘读取速度慢")
    
    if not torch.cuda.is_available():
        bottlenecks.append("未使用GPU加速")
    
    if bottlenecks:
        for bottleneck in bottlenecks:
            print(f"❌ {bottleneck}")
    else:
        print("✅ 未发现明显硬件瓶颈")
    
    # 6. 优化建议
    print(f"\n💡 速度优化建议:")
    
    print("1. 数据加载优化:")
    if memory.available / (1024**3) > 8:
        print("   ✅ 启用内存缓存: cache='ram'")
        print("   ✅ 增加workers: workers=6-8")
    else:
        print("   ⚠️  使用磁盘缓存: cache=True")
        print("   ⚠️  适中workers: workers=4")
    
    print("\n2. 训练参数优化:")
    print("   🔧 减少数据增强强度")
    print("   🔧 更早关闭Mosaic增强")
    print("   🔧 关闭不必要的图表生成")
    print("   🔧 启用矩形训练 rect=True")
    
    print("\n3. 硬件优化:")
    if torch.cuda.is_available():
        print("   ✅ 启用混合精度训练 amp=True")
        print("   ✅ 启用GPU内存锁定 pin_memory=True")
    print("   💾 使用SSD存储数据集")
    print("   🧠 增加系统内存")
    
    return {
        'memory_gb': memory.total / (1024**3),
        'memory_available_gb': memory.available / (1024**3),
        'disk_write_speed': write_speed,
        'disk_read_speed': read_speed,
        'gpu_available': torch.cuda.is_available(),
        'bottlenecks': bottlenecks
    }

if __name__ == "__main__":
    result = diagnose_training_speed()
    
    print(f"\n🎯 推荐配置:")
    if result['memory_gb'] >= 16:
        print("💪 高性能配置: workers=8, cache='ram', batch=-1")
    elif result['memory_gb'] >= 12:
        print("⚖️  平衡配置: workers=6, cache=True, batch=-1")
    else:
        print("🛡️  保守配置: workers=4, cache=True, batch=16") 