"""
创新点4: C2f-EMSCP-AAF + TADDH
Enhanced Multi-Scale Conv Plus + Adaptive Attention Fusion + Task-Aware Dynamic Detection Head
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

from ultralytics.nn.modules.conv import Conv, RepConv
from ultralytics.nn.modules.block import C2f, Bottleneck

__all__ = ['EMSConvP', 'AdaptiveAttentionFusion', 'Bottleneck_EMSCP_AAF', 'C2f_EMSCP_AAF']


class EMSConvP(nn.Module):
    """Enhanced Multi-Scale Conv Plus - 增强多尺度卷积金字塔"""
    def __init__(self, channel=256, kernels=[1, 3, 5, 7]):
        super().__init__()
        self.channel = channel
        self.groups = len(kernels)
        
        # 确保通道数能被组数整除
        if channel % self.groups != 0:
            # 调整通道数到最近的可整除值
            channel = ((channel + self.groups - 1) // self.groups) * self.groups
            self.channel = channel
        
        self.group_ch = channel // self.groups
        assert self.group_ch >= 16, f'group channel must >= 16, but got {self.group_ch}'

        # 为每个组创建卷积
        self.convs = nn.ModuleList([])
        for ks in kernels:
            self.convs.append(Conv(self.group_ch, self.group_ch, ks))
        
        # 输入通道调整（如果需要）
        self.input_adjust = Conv(channel, self.channel, 1) if channel != self.channel else nn.Identity()
        
        # 最终1x1卷积 - 输出回原始通道数
        self.conv_1x1 = Conv(self.channel, channel, 1)
        self.original_channel = channel
        
    def forward(self, x):
        # 调整输入通道数（如果需要）
        x = self.input_adjust(x)
        
        # 分组处理
        bs, _, h, w = x.shape
        x_group = x.view(bs, self.groups, self.group_ch, h, w)
        
        # 对每个组应用对应的卷积
        x_convs = []
        for i in range(self.groups):
            x_convs.append(self.convs[i](x_group[:, i]))
        
        # 合并所有组的输出
        x_convs = torch.cat(x_convs, dim=1)
        x_convs = self.conv_1x1(x_convs)
        
        return x_convs


class AdaptiveAttentionFusion(nn.Module):
    """Adaptive Attention Fusion - 自适应注意力融合"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.channels = channels
        
        # 通道注意力
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False),
            nn.Sigmoid()
        )
        
        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(2, 1, kernel_size=7, padding=3, bias=False),
            nn.Sigmoid()
        )
        
        # 自适应融合权重
        self.fusion_conv = nn.Conv2d(channels, channels, 1, bias=False)
        self.fusion_bn = nn.BatchNorm2d(channels)
        
    def forward(self, x):
        # 通道注意力
        ca = self.channel_attention(x)
        x_ca = x * ca
        
        # 空间注意力
        avg_out = torch.mean(x_ca, dim=1, keepdim=True)
        max_out, _ = torch.max(x_ca, dim=1, keepdim=True)
        sa_input = torch.cat([avg_out, max_out], dim=1)
        sa = self.spatial_attention(sa_input)
        x_sa = x_ca * sa
        
        # 自适应融合
        x_fused = self.fusion_conv(x_sa)
        x_fused = self.fusion_bn(x_fused)
        
        # 残差连接
        return x + x_fused


class Bottleneck_EMSCP_AAF(Bottleneck):
    """Enhanced Bottleneck with EMSCP and Adaptive Attention Fusion"""
    def __init__(self, c1, c2, shortcut=True, g=1, k=(3, 3), e=0.5):
        super().__init__(c1, c2, shortcut, g, k, e)
        c_ = int(c2 * e)  # hidden channels
        
        # 确保c_至少为16且能被4整除（EMSConvP的要求）
        if c_ < 64:
            c_ = max(64, c2)
        
        # 使用EMSConvP替换第二个卷积
        self.cv2 = EMSConvP(c_)
        
        # EMSConvP输出通道数与c_相同，如果与c2不同需要调整
        if c_ != c2:
            self.channel_adjust = Conv(c_, c2, 1)
        else:
            self.channel_adjust = nn.Identity()

        # 添加自适应注意力融合
        self.aaf = AdaptiveAttentionFusion(c2)

        # 残差连接条件
        self.add = shortcut and c1 == c2
        
    def forward(self, x):
        """Forward pass through the bottleneck with AAF."""
        x1 = self.cv1(x)
        y = self.cv2(x1)
        
        # 通道调整（确保输出通道数正确）
        y = self.channel_adjust(y)

        # 应用自适应注意力融合
        y = self.aaf(y)

        # 残差连接
        return x + y if self.add else y


class C2f_EMSCP_AAF(nn.Module):
    """C2f with EMSCP and Adaptive Attention Fusion"""
    def __init__(self, c1, c2, n=1, shortcut=False, g=1, e=0.5):
        super().__init__()
        # 确保所有参数都是有效的整数
        c1, c2, n = int(c1), int(c2), int(n)
        self.c = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, 2 * self.c, 1, 1)
        self.cv2 = Conv((2 + n) * self.c, c2, 1)  # optional act=FReLU(c2)

        # 使用增强的Bottleneck
        self.m = nn.ModuleList(
            Bottleneck_EMSCP_AAF(self.c, self.c, shortcut, g, k=(3, 3), e=1.0)
            for _ in range(n)
        )

        # 在最后添加全局自适应注意力融合
        self.global_aaf = AdaptiveAttentionFusion(c2)
        
    def forward(self, x):
        """Forward pass through C2f layer with AAF."""
        # 标准C2f流程
        y = list(self.cv1(x).chunk(2, 1))
        y.extend(m(y[-1]) for m in self.m)
        output = self.cv2(torch.cat(y, 1))

        # 应用全局自适应注意力融合
        output = self.global_aaf(output)

        return output


if __name__ == "__main__":
    print("🧪 测试创新点4模块...")
    
    # 测试EMSConvP
    x = torch.randn(1, 256, 32, 32)
    emscp = EMSConvP(256)
    out = emscp(x)
    print(f"✅ EMSConvP: {x.shape} -> {out.shape}")
    
    # 测试AdaptiveAttentionFusion
    aaf = AdaptiveAttentionFusion(256)
    out = aaf(x)
    print(f"✅ AdaptiveAttentionFusion: {x.shape} -> {out.shape}")
    
    # 测试Bottleneck_EMSCP_AAF
    bottleneck = Bottleneck_EMSCP_AAF(256, 256)
    out = bottleneck(x)
    print(f"✅ Bottleneck_EMSCP_AAF: {x.shape} -> {out.shape}")
    
    # 测试C2f_EMSCP_AAF
    c2f = C2f_EMSCP_AAF(256, 256, n=3)
    out = c2f(x)
    print(f"✅ C2f_EMSCP_AAF: {x.shape} -> {out.shape}")
    
    print("🎉 所有模块测试成功！")
