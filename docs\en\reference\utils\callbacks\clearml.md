---
description: Learn how to integrate ClearML with Ultralytics YOLO using detailed callbacks for pretraining, training, validation, and final logging.
keywords: Ultralytics, YOLO, ClearML, integration, callbacks, pretraining, training, validation, logging, AI, machine learning
---

# Reference for `ultralytics/utils/callbacks/clearml.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/clearml.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/clearml.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/clearml.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.utils.callbacks.clearml._log_debug_samples

<br><br>

## ::: ultralytics.utils.callbacks.clearml._log_plot

<br><br>

## ::: ultralytics.utils.callbacks.clearml.on_pretrain_routine_start

<br><br>

## ::: ultralytics.utils.callbacks.clearml.on_train_epoch_end

<br><br>

## ::: ultralytics.utils.callbacks.clearml.on_fit_epoch_end

<br><br>

## ::: ultralytics.utils.callbacks.clearml.on_val_end

<br><br>

## ::: ultralytics.utils.callbacks.clearml.on_train_end

<br><br>
