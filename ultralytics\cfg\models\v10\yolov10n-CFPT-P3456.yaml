# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv10 object detection model. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov10n.yaml' will call yolov10.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, SCDown, [512, 3, 2]] # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, SCDown, [1024, 3, 2]] # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SP<PERSON><PERSON>, [1024, 5]] # 9
  - [-1, 1, PSA, [1024]] # 10

head:
  - [4, 1, Conv, [256]]  # 11-P3/8
  - [6, 1, Conv, [256]]  # 12-P4/16
  - [10, 1, Conv, [256]]  # 13-P5/32

  - [[11, 12, 13], 1, CrossLayerChannelAttention, []] # 14
  - [14, 1, GetIndexOutput, [0]] # 15-P3/8
  - [14, 1, GetIndexOutput, [1]] # 16-P4/16
  - [14, 1, GetIndexOutput, [2]] # 17-P5/32

  - [[15, 16, 17], 1, CrossLayerSpatialAttention, []] # 18
  - [18, 1, GetIndexOutput, [0]] # 19-P3/8
  - [18, 1, GetIndexOutput, [1]] # 20-P4/16
  - [18, 1, GetIndexOutput, [2]] # 21-P5/32

  - [[11, 19], 1, Add, []] # 22-P3/8
  - [[12, 20], 1, Add, []] # 23-P4/16
  - [[13, 21], 1, Add, []] # 24-P5/32

  - [-1, 1, Conv, [512, 3, 2]] # 25-P6/64

  - [[22, 23, 24, 25], 1, v10Detect, [nc]] # Detect(P3, P4, P5, P6)