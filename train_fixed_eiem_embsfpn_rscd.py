import warnings, os
import torch
import torch.nn as nn
import multiprocessing as mp
warnings.filterwarnings('ignore')
from ultralytics import YOLO

# 修复版EIEM-EMBSFPN-RSCD训练脚本
# 基于问题诊断结果的针对性改进

if __name__ == '__main__':
    # Windows兼容性设置
    try:
        mp.set_start_method('spawn')
    except RuntimeError:
        pass
    
    print("=" * 80)
    print("🔧 修复版EIEM-EMBSFPN-RSCD模型训练")
    print("=" * 80)
    print("📋 问题诊断结果:")
    print("   ❌ 原始组合在相同轮数下性能下降37%")
    print("   🔧 采用渐进式训练和参数优化策略")
    print("   🎯 目标: 至少达到YOLOv8n的性能水平")
    print("=" * 80)
    
    # 阶段1: 首先测试单个C2f-EIEM模块
    print("\n🔬 阶段1: 测试C2f-EIEM模块单独效果")
    print("-" * 60)
    
    # 1. 加载C2f-EIEM配置
    print("📝 加载C2f-EIEM单独配置...")
    eiem_model = YOLO('ultralytics/cfg/models/v8/yolov8-C2f-EIEM.yaml')
    
    # 2. 加载预训练权重
    print("📦 加载预训练权重...")
    eiem_model.load('yolov8n.pt')
    
    # 3. C2f-EIEM模块测试训练 (快速验证)
    print("🚀 开始C2f-EIEM模块验证训练...")
    
    eiem_results = eiem_model.train(
        data='fire-smoke-dataset.yaml',
        epochs=10,  # 快速验证，10轮即可看出趋势
        lr0=0.001,  # 标准学习率
        batch=16,
        imgsz=640,
        patience=50,
        device=0,
        workers=0,
        cache=False,
        
        # 基础训练参数
        optimizer='AdamW',
        weight_decay=0.0005,
        warmup_epochs=3,
        warmup_bias_lr=0.1,
        
        # 损失权重
        box=7.5,
        cls=0.5,
        dfl=1.5,
        
        # 数据增强
        hsv_h=0.015,
        hsv_s=0.7,
        hsv_v=0.4,
        degrees=0.5,
        translate=0.1,
        scale=0.5,
        shear=0.0,
        perspective=0.0,
        flipud=0.0,
        fliplr=0.5,
        mosaic=0.8,
        mixup=0.0,
        copy_paste=0.0,
        
        # 输出设置
        save=True,
        save_period=5,
        plots=True,
        name='c2f-eiem-validation'
    )
    
    print("✅ C2f-EIEM模块验证完成")
    
    # 阶段2: 简化EMBSFPN集成
    print("\n🔬 阶段2: 简化EMBSFPN集成测试")
    print("-" * 60)
    
    # 1. 加载简化配置
    print("📝 加载简化EMBSFPN配置...")
    simplified_model = YOLO('ultralytics/cfg/models/v8/yolov8-EIEM-EMBSFPN-RSCD-progressive.yaml')
    
    # 2. 加载预训练权重
    print("📦 加载预训练权重...")
    simplified_model.load('yolov8n.pt')
    
    # 3. 简化版本训练
    print("🚀 开始简化版本训练...")
    
    simplified_results = simplified_model.train(
        data='fire-smoke-dataset.yaml',
        epochs=15,  # 稍长一点验证收敛性
        lr0=0.0008,  # 稍低学习率适应复杂性
        batch=16,
        imgsz=640,
        patience=50,
        device=0,
        workers=0,
        cache=False,
        
        # 复杂模型优化参数
        optimizer='AdamW',
        weight_decay=0.001,  # 增加正则化
        warmup_epochs=5,     # 延长预热
        warmup_bias_lr=0.1,
        
        # 调整损失权重
        box=8.0,   # 增强定位
        cls=0.4,   # 降低分类权重
        dfl=1.8,   # 增强DFL
        
        # 适配复杂模型的数据增强
        hsv_h=0.015,
        hsv_s=0.65,  # 稍微降低饱和度变化
        hsv_v=0.4,
        degrees=0.3,   # 减少旋转
        translate=0.08, # 减少平移
        scale=0.4,     # 减少缩放
        shear=0.0,
        perspective=0.0,
        flipud=0.0,
        fliplr=0.5,
        mosaic=0.6,    # 降低mosaic概率
        mixup=0.0,
        copy_paste=0.0,
        close_mosaic=10,  # 提前关闭mosaic
        
        # 输出设置
        save=True,
        save_period=5,
        plots=True,
        name='simplified-embsfpn-test'
    )
    
    print("✅ 简化EMBSFPN测试完成")
    
    # 阶段3: 完整模型优化训练
    print("\n🔬 阶段3: 完整模型优化训练")
    print("-" * 60)
    
    # 1. 加载完整配置
    print("📝 加载完整优化配置...")
    final_model = YOLO('ultralytics/cfg/models/v8/yolov8-EIEM-EMBSFPN-RSCD-ultimate.yaml')
    
    # 2. 加载预训练权重
    print("📦 加载预训练权重...")
    final_model.load('yolov8n.pt')
    
    # 3. 完整优化训练
    print("🚀 开始完整模型优化训练...")
    
    final_results = final_model.train(
        data='fire-smoke-dataset.yaml',
        epochs=50,  # 中等长度，观察收敛
        lr0=0.0005,  # 更低的学习率
        lrf=0.01,    # 最终学习率比例
        batch=16,
        imgsz=640,
        patience=50,
        device=0,
        workers=0,
        cache=False,
        
        # 针对复杂架构的优化参数
        optimizer='AdamW',
        weight_decay=0.002,   # 强正则化
        warmup_epochs=8,      # 长预热期
        warmup_momentum=0.8,
        warmup_bias_lr=0.1,
        
        # 精细调整的损失权重
        box=8.5,     # 强化定位学习
        cls=0.3,     # 降低分类权重防过拟合
        dfl=2.0,     # 增强分布损失
        
        # 保守的数据增强策略
        hsv_h=0.01,    # 最小色相变化
        hsv_s=0.6,     # 适度饱和度
        hsv_v=0.3,     # 适度明度
        degrees=0.2,   # 最小旋转
        translate=0.05, # 最小平移
        scale=0.3,     # 最小缩放
        shear=0.0,     # 不使用剪切
        perspective=0.0, # 不使用透视
        flipud=0.0,    # 不垂直翻转
        fliplr=0.5,    # 保持水平翻转
        mosaic=0.4,    # 低概率mosaic
        mixup=0.0,     # 不使用mixup
        copy_paste=0.0, # 不使用copy_paste
        close_mosaic=8, # 早期关闭mosaic
        
        # 学习率调度
        cos_lr=True,   # 使用余弦退火
        
        # 输出设置
        save=True,
        save_period=10,
        plots=True,
        name='fixed-eiem-embsfpn-rscd-ultimate',
        
        # 验证设置
        val=True,
        split='val'
    )
    
    print("✅ 完整模型优化训练完成")
    
    # 训练结果分析
    print("\n" + "=" * 80)
    print("📊 训练结果分析")
    print("=" * 80)
    
    print("\n🔍 建议对比分析:")
    print("1. 比较C2f-EIEM单独效果 vs 标准YOLOv8n")
    print("2. 评估简化EMBSFPN是否提升或损害性能") 
    print("3. 检查完整模型在相同轮数下的表现")
    print("4. 使用以下脚本进行公平对比:")
    print("   python yolov8_eiem_embsfpn_rscd_vs_yolov8n_analysis.py")
    
    print("\n💡 优化策略总结:")
    print("- 降低学习率: 0.001 → 0.0008 → 0.0005")
    print("- 增加正则化: weight_decay逐步提升")
    print("- 延长预热期: 适应复杂架构")
    print("- 调整损失权重: 平衡定位和分类")
    print("- 保守数据增强: 避免过度扰动")
    print("- 早期关闭增强: 稳定训练后期")
    
    print("\n🎯 预期结果:")
    print("- C2f-EIEM: 预期小幅提升(2-5%)")
    print("- 简化EMBSFPN: 预期持平或轻微提升")
    print("- 完整优化版本: 预期达到或超越YOLOv8n基线")
    
    print("\n" + "=" * 80) 