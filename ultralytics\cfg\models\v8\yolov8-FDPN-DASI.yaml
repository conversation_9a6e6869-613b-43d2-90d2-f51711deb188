# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv8 object detection model with P3-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 2  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n.yaml' will call yolov8.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]  # YOLOv8n summary: 225 layers,  3157200 parameters,  3157184 gradients,   8.9 GFLOPs
  s: [0.33, 0.50, 1024]  # YOLOv8s summary: 225 layers, 11166560 parameters, 11166544 gradients,  28.8 GFLOPs
  m: [0.67, 0.75, 768]   # YOLOv8m summary: 295 layers, 25902640 parameters, 25902624 gradients,  79.3 GFLOPs
  l: [1.00, 1.00, 512]   # YOLOv8l summary: 365 layers, 43691520 parameters, 43691504 gradients, 165.7 GFLOPs
  x: [1.00, 1.25, 512]   # YOLOv8x summary: 365 layers, 68229648 parameters, 68229632 gradients, 258.5 GFLOPs

# YOLOv8.0n backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]]  # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]]  # 1-P2/4
  - [-1, 3, C2f, [128, True]]
  - [-1, 1, Conv, [256, 3, 2]]  # 3-P3/8
  - [-1, 6, C2f, [256, True]]
  - [-1, 1, Conv, [512, 3, 2]]  # 5-P4/16
  - [-1, 6, C2f, [512, True]]
  - [-1, 1, Conv, [1024, 3, 2]]  # 7-P5/32
  - [-1, 3, C2f, [1024, True]]
  - [-1, 1, SPPF, [1024, 5]]  # 9

# YOLOv8.0n head
head:
  - [[9, 6, 4], 1, DASI, [512]] # 10-P4/16

  - [10, 1, Conv, [256, 3, 2]] # 11-P5/32
  - [[-1, 9], 1, Concat, [1]]  
  - [-1, 3, C2f, [512]]  # 13-P5/32

  - [10, 1, nn.Upsample, [None, 2, 'nearest']] # 14-P3/8
  - [[-1, 4], 1, Concat, [1]]  
  - [-1, 3, C2f, [256]]  # 16-P3/8

  - [[13, 10, 16], 1, DASI, [512]] # 17-P4/16

  - [17, 1, Conv, [256, 3, 2]] # 18-P5/32
  - [[11, 18, 13], 1, Concat, [1]]  
  - [-1, 3, C2f, [512]]  # 20-P5/32

  - [17, 1, nn.Upsample, [None, 2, 'nearest']] # 21-P3/8
  - [[14, 21, 16], 1, Concat, [1]]  
  - [-1, 3, C2f, [256]]  # 23-P3/8

  - [[23, 17, 20], 1, Detect, [nc]]  # Detect(P3, P4, P5)