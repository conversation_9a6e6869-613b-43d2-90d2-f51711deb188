#!/usr/bin/env python
"""
配置GPU加速环境
根据使用说明安装CUDA版本的PyTorch
"""

import subprocess
import sys
import os
import warnings
warnings.filterwarnings('ignore')

def run_command_safe(cmd, description="", ignore_errors=False):
    """安全运行命令并处理编码问题"""
    print(f"\n{'='*60}")
    print(f"正在执行: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*60}")
    
    try:
        # 设置环境变量解决编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        result = subprocess.run(
            cmd, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8',
            errors='ignore',
            env=env
        )
        print("✅ 成功!")
        if result.stdout:
            print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        if ignore_errors:
            print(f"⚠️  警告: {e}")
            return False
        else:
            print(f"❌ 失败: {e}")
            if e.stdout:
                print("标准输出:", e.stdout[:300])
            if e.stderr:
                print("错误输出:", e.stderr[:300])
            return False

def check_gpu_availability():
    """检查GPU和CUDA环境"""
    print("🔍 检查GPU和CUDA环境...")
    
    # 检查NVIDIA GPU
    try:
        result = subprocess.run("nvidia-smi", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 检测到NVIDIA GPU:")
            print(result.stdout[:500])
            return True
        else:
            print("❌ 未检测到NVIDIA GPU或驱动")
            return False
    except FileNotFoundError:
        print("❌ nvidia-smi命令不可用，请安装NVIDIA驱动")
        return False

def install_cuda_pytorch():
    """安装CUDA版本的PyTorch"""
    print("\n🚀 安装CUDA版本的PyTorch...")
    
    # 使用清华镜像源
    mirror = "https://pypi.tuna.tsinghua.edu.cn/simple"
    
    # 卸载当前的CPU版本
    print("📦 卸载当前的CPU版本PyTorch...")
    run_command_safe("pip uninstall torch torchvision torchaudio -y", "卸载CPU版本PyTorch", ignore_errors=True)
    
    # 安装CUDA版本 (根据使用说明推荐的版本)
    print("📦 安装CUDA版本PyTorch...")
    
    # 方案1: 使用pip安装CUDA版本
    cuda_install_cmd = f"pip install torch==2.2.2 torchvision==0.17.2 torchaudio --index-url https://download.pytorch.org/whl/cu121"
    success = run_command_safe(cuda_install_cmd, "安装CUDA版本PyTorch (方案1)", ignore_errors=True)
    
    if not success:
        # 方案2: 使用conda安装
        print("📦 尝试使用conda安装...")
        run_command_safe("conda install pytorch==2.2.2 torchvision==0.17.2 torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia", "使用conda安装CUDA版本", ignore_errors=True)

def install_cuda_mmcv():
    """安装CUDA版本的MMCV"""
    print("\n🔧 安装CUDA版本的MMCV...")
    
    # 卸载当前的CPU版本MMCV
    run_command_safe("pip uninstall mmcv -y", "卸载CPU版本MMCV", ignore_errors=True)
    
    # 安装CUDA版本的MMCV
    # 根据PyTorch版本选择对应的MMCV版本
    mmcv_cmd = 'mim install "mmcv>=2.0.0"'
    run_command_safe(mmcv_cmd, "安装CUDA版本MMCV", ignore_errors=True)

def test_gpu_functionality():
    """测试GPU功能"""
    print("\n🧪 测试GPU功能...")
    
    test_code = '''
import torch
import warnings
warnings.filterwarnings('ignore')

print("🔥 GPU环境测试:")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"GPU数量: {torch.cuda.device_count()}")
    for i in range(torch.cuda.device_count()):
        print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # 测试GPU计算
    try:
        x = torch.randn(1000, 1000).cuda()
        y = torch.randn(1000, 1000).cuda()
        z = torch.matmul(x, y)
        print("✅ GPU计算测试通过!")
    except Exception as e:
        print(f"❌ GPU计算测试失败: {e}")
        
    # 测试YOLO模型
    try:
        from ultralytics import YOLO
        model = YOLO('ultralytics/cfg/models/v8/yolov8n.yaml')
        
        # 尝试将模型移到GPU
        if torch.cuda.is_available():
            device = torch.device('cuda:0')
            model.model.to(device)
            print("✅ YOLO模型GPU加载成功!")
        
    except Exception as e:
        print(f"⚠️  YOLO GPU测试: {e}")
        
else:
    print("❌ CUDA不可用，请检查安装")
'''
    
    # 将测试代码写入文件
    with open("test_gpu.py", "w", encoding="utf-8") as f:
        f.write(test_code)
    
    # 运行测试
    run_command_safe("python test_gpu.py", "GPU功能测试", ignore_errors=True)

def create_gpu_training_example():
    """创建GPU训练示例"""
    print("\n📝 创建GPU训练示例...")
    
    training_example = '''
#!/usr/bin/env python
"""
GPU训练示例
"""

import torch
from ultralytics import YOLO
import warnings
warnings.filterwarnings('ignore')

def main():
    print("🚀 GPU训练示例")
    
    # 检查GPU
    if not torch.cuda.is_available():
        print("❌ GPU不可用，将使用CPU训练")
        device = 'cpu'
    else:
        print(f"✅ 检测到GPU: {torch.cuda.get_device_name(0)}")
        device = 'cuda:0'
    
    # 创建模型
    model = YOLO('ultralytics/cfg/models/v8/yolov8n.yaml')
    
    # 示例训练参数
    train_args = {
        'data': 'dataset/data.yaml',  # 数据集配置文件
        'epochs': 100,
        'batch': 16,
        'imgsz': 640,
        'device': device,
        'workers': 4,
        'patience': 50,
        'save': True,
        'plots': True,
        'val': True,
    }
    
    print("🎯 训练参数:")
    for key, value in train_args.items():
        print(f"  {key}: {value}")
    
    print("\\n💡 开始训练命令:")
    print(f"model.train(**train_args)")
    
    # 注意：实际训练需要准备数据集
    print("\\n⚠️  注意：请确保数据集路径正确后再开始训练")

if __name__ == "__main__":
    main()
'''
    
    with open("gpu_training_example.py", "w", encoding="utf-8") as f:
        f.write(training_example)
    
    print("✅ 创建了GPU训练示例: gpu_training_example.py")

def generate_gpu_setup_summary():
    """生成GPU配置总结"""
    print("\n" + "="*60)
    print("📋 GPU环境配置总结")
    print("="*60)
    
    print("\n✅ 完成的配置步骤:")
    print("1. 检查了GPU和CUDA环境")
    print("2. 安装了CUDA版本的PyTorch")
    print("3. 安装了CUDA版本的MMCV")
    print("4. 测试了GPU功能")
    print("5. 创建了GPU训练示例")
    
    print("\n🚀 下一步操作:")
    print("1. 运行 'python test_gpu.py' 验证GPU环境")
    print("2. 运行 'python gpu_training_example.py' 查看训练示例")
    print("3. 准备数据集并开始GPU训练")
    
    print("\n💡 GPU训练优势:")
    print("- 训练速度显著提升 (通常5-10倍)")
    print("- 支持更大的batch size")
    print("- 可以训练更复杂的模型")
    print("- 支持混合精度训练(AMP)")
    
    print("\n⚠️  注意事项:")
    print("- 确保GPU显存足够 (建议8GB+)")
    print("- 根据GPU显存调整batch size")
    print("- 某些高级模块需要CUDA环境才能编译")

def main():
    print("🚀 开始配置GPU加速环境...")
    print("基于使用说明推荐配置: torch 2.2.2+cu121")
    
    # 检查GPU
    gpu_available = check_gpu_availability()
    
    if not gpu_available:
        print("\n❌ 未检测到GPU环境，请先安装NVIDIA驱动")
        print("💡 安装步骤:")
        print("1. 从NVIDIA官网下载并安装GPU驱动")
        print("2. 重启计算机")
        print("3. 重新运行此脚本")
        return
    
    # 安装CUDA版本的PyTorch
    install_cuda_pytorch()
    
    # 安装CUDA版本的MMCV
    install_cuda_mmcv()
    
    # 测试GPU功能
    test_gpu_functionality()
    
    # 创建训练示例
    create_gpu_training_example()
    
    # 生成总结
    generate_gpu_setup_summary()

if __name__ == "__main__":
    main() 