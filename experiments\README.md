# 🧪 Experiments文件夹 - 训练实验脚本集合

这个文件夹包含了各种实验性的训练脚本，用于测试不同的模型组合、训练策略和优化方案。

## 📁 实验脚本列表

### 🔄 组合模型实验
- **train_safe_combined_optimized.py** - 安全组合模型优化训练
  - C2f-EMSCP + TADDH 组合
  - 内存优化配置
  - 保守的训练参数
  - 使用方法：`python experiments/train_safe_combined_optimized.py`

- **train_safe_combined.py** - 安全组合模型训练
  - 基础组合模型实验
  - 稳定性优先配置
  - 使用方法：`python experiments/train_safe_combined.py`

- **train_combined_model.py** - 标准组合模型训练
  - 多模块组合实验
  - 标准训练配置
  - 使用方法：`python experiments/train_combined_model.py`

- **train_simple_combined.py** - 简化组合训练
  - 轻量级组合实验
  - 快速验证用
  - 使用方法：`python experiments/train_simple_combined.py`

### ⚡ 基础训练实验
- **train_simple.py** - 最简训练脚本
  - 最小化配置
  - 快速测试用
  - 使用方法：`python experiments/train_simple.py`

### 🔄 训练恢复工具
- **resume_train.py** - 恢复训练脚本
  - 从断点恢复训练
  - 简单的恢复逻辑
  - 使用方法：`python experiments/resume_train.py`

- **continue_train.py** - 继续训练脚本
  - 完整的训练继续功能
  - 支持配置调整
  - 使用方法：`python experiments/continue_train.py`

## 🎯 实验目的

### 模型组合验证
这些脚本主要用于验证不同模块组合的效果：
- **C2f-EMSCP + TADDH**: 测试骨干网络与检测头的组合效果
- **内存优化**: 验证在资源受限情况下的训练稳定性
- **参数调优**: 对比不同训练策略的效果

### 训练策略对比
- **安全配置 vs 标准配置**: 对比保守参数与标准参数的效果
- **简化配置**: 测试最小化配置的可行性
- **恢复策略**: 验证不同的训练恢复方案

## 📊 实验记录

### 已完成的实验
1. **C2f-EMSCP + TADDH 组合**
   - 结果：成功组合，但需要通道适配
   - mAP50: 约0.77+ 
   - 问题：通道数不匹配需要额外处理

2. **内存优化训练**
   - 结果：显著提升训练速度
   - 优化：从10分钟/epoch 降到 6-7分钟/epoch
   - 配置：workers=8, cache=ram, batch=63

### 待进行的实验
- [ ] 不同损失权重的对比实验
- [ ] 数据增强策略的消融实验
- [ ] 学习率调度策略对比
- [ ] 多尺度训练效果验证

## ⚠️ 使用注意事项

1. **实验性质**：这些脚本主要用于实验和测试，不保证稳定性
2. **资源消耗**：某些组合模型可能消耗更多GPU显存和计算资源
3. **结果记录**：建议为每次实验做好记录，包括配置和结果
4. **备份权重**：实验前请备份重要的训练权重

## 🔄 实验流程建议

### 新实验启动流程
1. **环境检查**: `python fix/speed_diagnosis.py`
2. **内存清理**: `python fix/free_memory.py`
3. **选择实验脚本**: 根据实验目的选择合适的脚本
4. **记录配置**: 保存实验配置和预期目标
5. **开始实验**: 运行选定的实验脚本
6. **结果分析**: 对比实验结果和基线

### 实验结果对比
建议使用以下指标对比实验效果：
- **精度指标**: mAP50, mAP50-95
- **速度指标**: FPS, 训练时间
- **资源消耗**: GPU显存, 内存使用
- **稳定性**: 训练过程是否稳定

## 📈 实验管理

### 结果记录模板
```
实验名称: [实验描述]
日期: [YYYY-MM-DD]
配置: [关键参数设置]
数据集: [使用的数据集]
基线对比: [对比的基线模型]
结果:
  - mAP50: X.XXX
  - mAP50-95: X.XXX
  - 训练时间: XX分钟/epoch
  - GPU显存: XX.XGB
结论: [实验结论和发现]
下一步: [后续实验计划]
```

---

*实验文件夹创建于2024年，用于YOLO模型改进和优化实验* 