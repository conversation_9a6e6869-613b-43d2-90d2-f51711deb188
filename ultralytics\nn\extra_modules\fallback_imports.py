"""
Fallback imports for missing dependencies
当某些依赖包缺失时，提供fallback实现
"""

import warnings

# Mamba SSM fallback
try:
    from mamba_ssm.ops.selective_scan_interface import selective_scan_fn
except ImportError:
    warnings.warn("mamba_ssm not available, using fallback implementation")
    def selective_scan_fn(*args, **kwargs):
        raise NotImplementedError("mamba_ssm not installed. Please install with: pip install mamba-ssm")

# NATTEN fallback  
try:
    import natten
except ImportError:
    warnings.warn("natten not available, using fallback implementation")
    class natten:
        @staticmethod
        def functional(*args, **kwargs):
            raise NotImplementedError("natten not installed. Please install with: pip install natten")

# DCNv3 fallback
try:
    from DCNv3 import DCNv3
except ImportError:
    warnings.warn("DCNv3 not available, using fallback implementation")
    class DCNv3:
        def __init__(self, *args, **kwargs):
            raise NotImplementedError("DCNv3 not compiled. Please compile the local module.")

# DCNv4 fallback
try:
    from ultralytics.nn.extra_modules.DCNv4_op.DCNv4.dcnv4 import DCNv4
except ImportError:
    warnings.warn("DCNv4 not available, using fallback implementation")
    class DCNv4:
        def __init__(self, *args, **kwargs):
            raise NotImplementedError("DCNv4 not compiled. Please compile the local module.")

# Export commonly used functions
__all__ = ['selective_scan_fn', 'natten', 'DCNv3', 'DCNv4']
