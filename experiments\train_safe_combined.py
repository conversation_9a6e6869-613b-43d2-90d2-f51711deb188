import warnings, os
import torch
import torch.nn as nn
warnings.filterwarnings('ignore')
from ultralytics import YOLO

# 安全的C2f-EMSCP + TADDH 组合模型训练脚本
# 完全独立实现，不影响原有代码结构

if __name__ == '__main__':
    print("=" * 60)
    print("安全的 C2f-EMSCP + TADDH 组合模型训练脚本")
    print("🔒 完全独立实现，不影响原有代码结构")
    print("=" * 60)
    
    # 1. 模型初始化 - 使用安全配置
    model = YOLO('ultralytics/cfg/models/v8/yolov8n-C2f-EMSCP-TADDH-safe.yaml')
    
    # 2. 加载预训练权重
    print("加载预训练权重...")
    model.load('yolov8n.pt')
    
    # 3. 优化训练参数
    print("开始安全模式训练...")
    model.train(
        # === 数据集配置 ===
        data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
        
        # === 基本训练参数 ===
        epochs=30,              # 训练轮数
        patience=60,             # 耐心值
        batch=-1,                # 自动选择batch size
        imgsz=640,
        
        # === 设备设置 ===
        device="0" if os.environ.get('CUDA_VISIBLE_DEVICES') != '-1' else "cpu",
        workers=4,  # 减少worker数量避免Windows共享内存问题
        
        # === 项目设置 ===
        project='runs/train',
        name='fire-smoke-dataset-safe-C2f-EMSCP-TADDH',
        exist_ok=True,
        
        # === 模型设置 ===
        pretrained=True,         # 使用预训练权重
        
        # === 优化器设置 ===
        optimizer="AdamW",
        lr0=0.001,              # 初始学习率
        lrf=0.0001,             # 最终学习率
        momentum=0.937,
        weight_decay=0.0008,    # 权重衰减
        
        # === 学习率调度 ===
        cos_lr=True,            # 余弦学习率调度
        warmup_epochs=5.0,      # 预热轮数
        warmup_momentum=0.8,
        warmup_bias_lr=0.01,    # 预热偏置学习率
        
        # === 损失权重优化 ===
        box=7.5,                # 框损失权重
        cls=0.5,                # 分类损失权重
        dfl=1.5,                # DFL损失权重
        
        # === 数据增强优化 ===
        hsv_h=0.015,            # 色调变化
        hsv_s=0.7,              # 饱和度变化
        hsv_v=0.4,              # 亮度变化
        degrees=10.0,           # 旋转角度
        translate=0.1,          # 平移
        scale=0.6,              # 缩放范围
        shear=0.0,
        perspective=0.0,
        flipud=0.0,
        fliplr=0.5,
        mosaic=0.6,             # 马赛克概率
        mixup=0.0,
        copy_paste=0.0,
        
        # === 特殊优化 ===
        close_mosaic=15,        # 马赛克关闭时间
        
        # === 其他优化设置 ===
        verbose=True,
        seed=42,
        deterministic=True,
        single_cls=False,
        rect=False,
        resume=False,
        amp=True,               # 启用混合精度
        fraction=1.0,
        profile=False,
        freeze=None,
        cache=True,
        val=True,
        plots=True,
        save=True,
        save_period=10,
        
        # === 高级设置 ===
        dropout=0.0,
        label_smoothing=0.0,
        
        # === 其他稳定性设置 ===
        half=False,             # 不使用半精度保持稳定
        
        # === 任务设置 ===
        task='detect',
        mode='train',
        
        # === NMS设置 ===
        iou=0.6,
        max_det=300,
        agnostic_nms=False,
    )
    
    print("=" * 60)
    print("安全的组合模型训练完成！")
    print("=" * 60)
    print("🔒 安全特性总结：")
    print("🔧 骨干网络: C2f-EMSCP (增强特征提取)")
    print("🎯 检测头: 自适应TADDH (独立实现)")
    print("⚡ 优化设置: 混合精度 + AdamW优化器")
    print("🛡️ 代码安全: 完全独立，不影响原有结构")
    print("📊 数据增强: 适度增强避免过拟合")
    print("🔄 学习率调度: 余弦退火 + 预热")
    print("=" * 60) 