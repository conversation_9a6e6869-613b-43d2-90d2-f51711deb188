#!/usr/bin/env python3
"""
批量修复Windows下Mamba编译时M_LOG2E未定义的问题
"""

import os
import re

def fix_m_log2e_in_file(file_path):
    """在指定文件中添加M_LOG2E定义"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含M_LOG2E定义
        if '#define M_LOG2E' in content:
            print(f"✅ {file_path} 已经包含M_LOG2E定义")
            return True
        
        # 检查是否需要修复（文件中使用了M_LOG2E）
        if 'M_LOG2E' not in content:
            print(f"⏭️ {file_path} 不需要修复（未使用M_LOG2E）")
            return True
        
        # 找到include语句的位置
        include_pattern = r'(#include\s+[<"][^>"]+[>"][\s\n]*)+?'
        match = re.search(include_pattern, content, re.MULTILINE)
        
        if match:
            # 在include语句后添加M_LOG2E定义
            insert_pos = match.end()
            fix_code = '''
// Fix for Windows MSVC: define M_LOG2E if not defined
#ifndef M_LOG2E
#define M_LOG2E 1.4426950408889634
#endif
'''
            new_content = content[:insert_pos] + fix_code + content[insert_pos:]
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 修复完成: {file_path}")
            return True
        else:
            print(f"❌ 无法找到include语句: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复Windows下Mamba编译问题...")
    
    # 需要修复的文件列表
    files_to_fix = [
        'ultralytics/nn/extra_modules/selective_scan/csrc/selective_scan/cusnrow/selective_scan_fwd_kernel_nrow.cuh',
        'ultralytics/nn/extra_modules/selective_scan/csrc/selective_scan/cusnrow/selective_scan_bwd_kernel_nrow.cuh',
        'ultralytics/nn/extra_modules/selective_scan/csrc/selective_scan/cusndstate/selective_scan_fwd_kernel_ndstate.cuh',
        'ultralytics/nn/extra_modules/selective_scan/csrc/selective_scan/cusndstate/selective_scan_bwd_kernel_ndstate.cuh',
        'ultralytics/nn/extra_modules/selective_scan/csrc/selective_scan/cus/selective_scan_fwd_kernel.cuh',
        'ultralytics/nn/extra_modules/selective_scan/csrc/selective_scan/cus/selective_scan_bwd_kernel.cuh',
    ]
    
    success_count = 0
    total_count = len(files_to_fix)
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_m_log2e_in_file(file_path):
                success_count += 1
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    print(f"\n📊 修复完成: {success_count}/{total_count} 文件修复成功")
    
    if success_count == total_count:
        print("🎉 所有文件修复完成！现在可以尝试编译Mamba了")
        print("运行命令: cd ultralytics/nn/extra_modules/mamba && python setup.py install")
    else:
        print("⚠️ 部分文件修复失败，请检查错误信息")

if __name__ == "__main__":
    main() 