@echo off
echo 正在设置代理环境变量...

REM 设置代理环境变量（根据您的Clash配置）
set http_proxy=http://127.0.0.1:7890
set https_proxy=http://127.0.0.1:7890
set HTTP_PROXY=http://127.0.0.1:7890
set HTTPS_PROXY=http://127.0.0.1:7890

echo ✅ 代理环境变量已设置:
echo   http_proxy = %http_proxy%
echo   https_proxy = %https_proxy%

REM 测试代理连接
echo.
echo 🌐 测试代理连接...
curl -I --connect-timeout 5 https://github.com
if %errorlevel% equ 0 (
    echo ✅ 代理连接正常
) else (
    echo ❌ 代理连接失败，请检查Clash是否正常运行
)

echo.
echo 💡 现在可以运行以下命令：
echo   python install_with_proxy.py
echo   或者直接使用 pip install 命令

pause 