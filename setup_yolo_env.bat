@echo off
echo 🐍 设置YOLO训练环境...

REM 设置Python路径
set PYTHON_PATH=D:\anaconda3\envs\yolov83\python.exe
set PATH=D:\anaconda3\envs\yolov83;D:\anaconda3\envs\yolov83\Scripts;%PATH%

echo ✅ Python环境已设置
echo 📍 Python路径: %PYTHON_PATH%

REM 验证环境
echo 🔍 验证环境...
%PYTHON_PATH% -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')"

echo.
echo 🚀 现在可以直接使用 python 命令了！
echo 💡 或者使用 %%PYTHON_PATH%% 变量

cmd /k
