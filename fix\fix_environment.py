#!/usr/bin/env python
"""
修复YOLO环境的脚本
解决编码问题和依赖安装
"""

import subprocess
import sys
import os
import warnings
warnings.filterwarnings('ignore')

def run_command_with_encoding(cmd, description="", ignore_errors=False):
    """运行命令并处理编码问题"""
    print(f"\n{'='*60}")
    print(f"正在执行: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*60}")
    
    try:
        # 设置环境变量解决编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        result = subprocess.run(
            cmd, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8',
            errors='ignore',
            env=env
        )
        print("✅ 成功!")
        if result.stdout:
            print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        if ignore_errors:
            print(f"⚠️  警告: {e}")
            return False
        else:
            print(f"❌ 失败: {e}")
            if e.stdout:
                print("标准输出:", e.stdout[:300])
            if e.stderr:
                print("错误输出:", e.stderr[:300])
            return False

def install_basic_packages():
    """安装基础包"""
    print("\n🔧 安装基础依赖包...")
    
    basic_packages = [
        "ninja",
        "timm",
        "einops",
        "packaging",
        "psutil",
        "pyyaml",
        "requests",
        "scipy",
        "torchvision",
        "tqdm",
        "matplotlib",
        "opencv-python",
        "pillow",
        "numpy",
    ]
    
    for package in basic_packages:
        run_command_with_encoding(
            f"pip install {package} --upgrade",
            f"安装 {package}",
            ignore_errors=True
        )

def install_optional_packages():
    """安装可选包（使用镜像源）"""
    print("\n🌐 尝试安装可选依赖包...")
    
    # 使用清华镜像源
    mirror = "https://pypi.tuna.tsinghua.edu.cn/simple"
    
    optional_packages = [
        ("mamba-ssm", "Mamba状态空间模型"),
        ("causal-conv1d", "因果卷积1D"),
        ("natten", "邻域注意力"),
    ]
    
    for package, desc in optional_packages:
        run_command_with_encoding(
            f"pip install {package} -i {mirror}",
            f"安装 {desc} ({package})",
            ignore_errors=True
        )

def test_imports():
    """测试导入"""
    print("\n🧪 测试环境...")
    
    test_cases = [
        ("torch", "PyTorch"),
        ("torchvision", "TorchVision"),
        ("ultralytics", "Ultralytics"),
        ("mmcv", "MMCV"),
        ("mmengine", "MMEngine"),
    ]
    
    success_count = 0
    for module, name in test_cases:
        try:
            __import__(module)
            print(f"✅ {name}: 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {name}: 导入失败 - {e}")
    
    print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 个核心模块可用")
    return success_count

def create_simple_test():
    """创建简单的YOLO测试"""
    test_code = '''
import warnings
warnings.filterwarnings('ignore')

try:
    from ultralytics import YOLO
    print("✅ YOLO导入成功")
    
    # 创建一个简单的模型实例
    model = YOLO('yolov8n.yaml')
    print("✅ YOLO模型创建成功")
    
    print("🎉 基础YOLO功能正常!")
    
except Exception as e:
    print(f"❌ YOLO测试失败: {e}")
    print("💡 建议检查torch和ultralytics安装")
'''
    
    with open("simple_yolo_test.py", "w", encoding="utf-8") as f:
        f.write(test_code)
    
    print("\n✅ 创建了简单测试文件: simple_yolo_test.py")

def main():
    print("🚀 开始修复YOLO环境...")
    print("🔧 解决编码问题和依赖安装")
    
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    # 1. 安装基础包
    install_basic_packages()
    
    # 2. 安装可选包
    install_optional_packages()
    
    # 3. 测试导入
    success_count = test_imports()
    
    # 4. 创建简单测试
    create_simple_test()
    
    print(f"\n{'='*60}")
    print("🎉 环境修复完成!")
    print(f"{'='*60}")
    
    if success_count >= 3:
        print("✅ 核心功能应该可以正常使用")
        print("💡 运行 'python simple_yolo_test.py' 测试基础功能")
    else:
        print("⚠️  部分核心模块有问题，可能需要重新安装torch")
    
    print("\n📋 后续步骤:")
    print("1. 运行 'python simple_yolo_test.py' 测试基础功能")
    print("2. 运行 'python test_env_step.py' 查看详细状态")
    print("3. 如果需要特定功能，请根据错误信息手动安装")

if __name__ == "__main__":
    main() 