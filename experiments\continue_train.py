import warnings, os
warnings.filterwarnings('ignore')
from ultralytics import YOLO

# 方法1：从现有权重继续训练5轮
if __name__ == '__main__':
    # 加载之前训练好的权重
    model = YOLO('runs/train/fire-smoke-dataset-yolov8-ASF/weights/best.pt')
    
    # 继续训练5轮
    model.train(
        # 数据集配置
        data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
        
        # 训练基本参数
        epochs=5,              # 继续训练5轮
        patience=40,           # 双分类需要更多耐心
        batch=-1,              # 自动选择最优batch size
        imgsz=640,             # 输入图像尺寸
        
        # 设备设置
        device="0" if os.environ.get('CUDA_VISIBLE_DEVICES') != '-1' else "cpu",
        workers=8,             # 数据加载线程数
        
        # 项目和名称设置 - 使用新的名称避免覆盖
        project='runs/train',
        name='fire-smoke-dataset-yolov8-ASF-continue',  # 新的文件夹名
        exist_ok=True,
        
        # 优化器设置 (继续使用之前的配置)
        optimizer="AdamW",
        lr0=0.0015,           # 可以降低学习率继续精调
        lrf=0.001,
        momentum=0.937,
        weight_decay=0.0005,
        
        # 学习率调度
        cos_lr=True,
        warmup_epochs=1.0,    # 减少预热轮数
        warmup_momentum=0.8,
        warmup_bias_lr=0.1,
        
        # 其他训练参数
        verbose=True,
        seed=42,
        deterministic=True,
        single_cls=False,
        rect=False,
        close_mosaic=5,
        resume=False,         # 不使用resume，而是从权重继续
        amp=False,
        fraction=1.0,
        profile=False,
        freeze=None,
        
        # 数据增强参数 (可以适当降低，因为模型已经预训练)
        hsv_h=0.015,          # 降低色调变化
        hsv_s=0.6,            # 降低饱和度变化
        hsv_v=0.4,            # 降低亮度变化
        degrees=10.0,         # 降低旋转
        translate=0.1,        # 降低平移
        scale=0.5,            # 降低缩放范围
        shear=0.0,
        perspective=0.0,
        flipud=0.0,
        fliplr=0.5,
        mosaic=0.5,           # 降低马赛克
        mixup=0.0,
        copy_paste=0.0,
        
        # 损失权重
        box=8.0,
        cls=1.0,
        dfl=2.0,
        
        # 缓存
        cache=True,
    ) 