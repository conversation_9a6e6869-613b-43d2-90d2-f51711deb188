# Ultralytics YOLO 🚀, AGPL-3.0 license
# Hyper-YOLO object detection model with N3-N5 outputs. 

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=hyper-yolon.yaml' will call hyper-yolo.yaml with scale 'n'
  # [depth, width, max_channels, threshold]
  n: [0.33, 0.25, 1024, 6]
  s: [0.33, 0.50, 1024, 8]
  m: [0.67, 0.75, 768, 10]
  l: [1.00, 1.00, 512, 10]
  x: [1.00, 1.25, 512, 12]

# Hyper-YOLO backbone
backbone:
  - [-1, 1, Conv, [64, 3, 2]]  # 0-B1/2
  - [-1, 1, Conv, [128, 3, 2]]  # 1
  - [-1, 3, <PERSON><PERSON>, [128, True, 2, 3]] # 2-B2/4
  - [-1, 1, Conv, [256, 3, 2]]  # 3
  - [-1, 6, <PERSON><PERSON>, [256, True, 2, 5]] # 4-B3/8
  - [-1, 1, Conv, [512, 3, 2]]  # 5
  - [-1, 6, <PERSON><PERSON>, [512, True, 2, 5]] # 6-B4/16
  - [-1, 1, Conv, [1024, 3, 2]]  # 7-P5/32
  - [-1, 3, MA<PERSON>, [1024, True, 2, 3]] # 8
  - [-1, 1, SPPF, [1024, 5]]  # 9-B5/32

# Hyper-YOLO head
head:
  # Semantic Collecting
  - [0, 1, nn.AvgPool2d, [8, 8, 0]] # 10
  - [2, 1, nn.AvgPool2d, [4, 4, 0]] # 11
  - [4, 1, nn.AvgPool2d, [2, 2, 0]] # 12
  - [9, 1, nn.Upsample, [None, 2, 'nearest']] # 13
  - [[10, 11, 12, 6, 13], 1, Concat, [1]]  # cat 14

  # Hypergraph Computation
  - [-1, 1, Conv, [512, 1, 1]] # 15
  - [-1, 1, HyperComputeModule, [512]] # 16
  - [-1, 3, MANet, [512, True, 2, 3]] # 17

  # Semantic Collecting
  - [-1, 1, nn.AvgPool2d, [2, 2, 0]] # 18
  - [[-1, 9], 1, Concat, [1]]  # cat 19
  - [-1, 1, Conv, [1024, 1, 1]] # 20 P5

  - [[17, 6], 1, Concat, [1]]  # cat 21
  - [-1, 3, MANet, [512, False, 2, 3]]  # 22 P4

  - [17, 1, nn.Upsample, [None, 2, 'nearest']] # 23
  - [[-1, 4], 1, Concat, [1]]  # cat 24
  - [-1, 3, MANet, [256, False, 2, 3]]  # 25 P3/N3

  - [-1, 1, Conv, [256, 3, 2]] # 26
  - [[-1, 22], 1, Concat, [1]]  # 27 cat 
  - [-1, 3, MANet, [512, False, 2, 3]]  # 28 N4

  - [-1, 1, Conv, [512, 3, 2]] # 29
  - [[-1, 20], 1, Concat, [1]]  # 30 cat
  - [-1, 3, C2f, [1024, False]]  # 31 N5

  - [[25, 28, 31], 1, Detect, [nc]]  # Detect(N3, N4, N5)