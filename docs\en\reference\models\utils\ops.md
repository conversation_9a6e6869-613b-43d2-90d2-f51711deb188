---
description: Explore the utilities and operations in Ultralytics models like HungarianMatcher and get_cdn_group. Learn how to optimize and manage model operations efficiently.
keywords: Ultralytics, models, utils, operations, HungarianMatcher, get_cdn_group, model optimization, pytorch, machine learning
---

# Reference for `ultralytics/models/utils/ops.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/utils/ops.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/utils/ops.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/utils/ops.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.models.utils.ops.HungarianMatcher

<br><br>

## ::: ultralytics.models.utils.ops.get_cdn_group

<br><br>
