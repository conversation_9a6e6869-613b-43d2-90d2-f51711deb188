"""
创新点2: C2f-EMSCP + 边缘信息增强特征金字塔 (EIFPN)
核心模块实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from ultralytics.nn.modules.conv import Conv
from ultralytics.nn.modules.block import C2f, Bottleneck

__all__ = ['SobelConv', 'EIEM', 'Bottleneck_EMSCP_EIEM', 'C2f_EMSCP_EIEM', 'EdgeEnhancedFusion']


class EMSConvP(nn.Module):
    """Enhanced Multi-Scale Conv Plus - 复制自原始实现"""
    def __init__(self, channel=256, kernels=[1, 3, 5, 7]):
        super().__init__()
        self.groups = len(kernels)
        min_ch = channel // self.groups
        assert min_ch >= 16, f'channel must Greater than {16 * self.groups}, but {channel}'
        
        self.convs = nn.ModuleList([])
        for ks in kernels:
            self.convs.append(Conv(c1=min_ch, c2=min_ch, k=ks))
        self.conv_1x1 = Conv(channel, channel, k=1)
        
    def forward(self, x):
        # 重新排列输入张量以分组处理
        bs, ch, h, w = x.shape
        x_group = x.view(bs, self.groups, ch // self.groups, h, w)
        
        # 对每个组应用对应的卷积
        x_convs = []
        for i in range(self.groups):
            x_convs.append(self.convs[i](x_group[:, i]))
        
        # 合并所有组的输出
        x_convs = torch.cat(x_convs, dim=1)
        x_convs = self.conv_1x1(x_convs)
        
        return x_convs


class SobelConv(nn.Module):
    """Sobel边缘检测卷积 - 优化版本"""
    def __init__(self, channels):
        super().__init__()
        self.channels = channels
        
        # Sobel算子
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)
        
        # 扩展到所有通道
        self.register_buffer('sobel_x_kernel', sobel_x.view(1, 1, 3, 3).repeat(channels, 1, 1, 1))
        self.register_buffer('sobel_y_kernel', sobel_y.view(1, 1, 3, 3).repeat(channels, 1, 1, 1))
        
    def forward(self, x):
        # 应用Sobel算子
        edge_x = F.conv2d(x, self.sobel_x_kernel, padding=1, groups=self.channels)
        edge_y = F.conv2d(x, self.sobel_y_kernel, padding=1, groups=self.channels)
        
        # 计算边缘强度
        edge_magnitude = torch.sqrt(edge_x**2 + edge_y**2 + 1e-6)
        
        return edge_magnitude


class EIEM(nn.Module):
    """边缘信息增强模块"""
    def __init__(self, inc, ouc):
        super().__init__()
        
        self.sobel_branch = SobelConv(inc)
        self.conv_branch = Conv(inc, inc, 3)
        self.conv1 = Conv(inc * 2, inc, 1)
        self.conv2 = Conv(inc, ouc, 1)
        
    def forward(self, x):
        # 边缘检测分支
        x_sobel = self.sobel_branch(x)
        
        # 空间特征分支
        x_conv = self.conv_branch(x)
        
        # 特征融合
        x_concat = torch.cat([x_sobel, x_conv], dim=1)
        x_feature = self.conv1(x_concat)
        
        # 残差连接
        x = self.conv2(x_feature + x)
        
        return x


class EdgeEnhancedFusion(nn.Module):
    """边缘增强融合模块"""
    def __init__(self, channels_list, fusion_mode='bifpn'):
        super().__init__()
        self.fusion_mode = fusion_mode
        self.channels_list = channels_list
        
        # 边缘检测模块
        self.edge_detectors = nn.ModuleList([
            SobelConv(ch) for ch in channels_list
        ])
        
        # 边缘特征融合
        self.edge_fusion = nn.ModuleList([
            Conv(ch * 2, ch, 1) for ch in channels_list
        ])
        
        if fusion_mode == 'bifpn':
            # BiFPN加权融合
            self.fusion_weights = nn.Parameter(
                torch.ones(len(channels_list), dtype=torch.float32), 
                requires_grad=True
            )
            self.relu = nn.ReLU()
            self.epsilon = 1e-4
        
    def forward(self, features):
        """
        features: list of feature maps [P3, P4, P5]
        """
        enhanced_features = []

        for i, (feat, edge_detector, edge_fusion) in enumerate(
            zip(features, self.edge_detectors, self.edge_fusion)
        ):
            # 提取边缘信息
            edge_feat = edge_detector(feat)

            # 融合原始特征和边缘特征
            combined = torch.cat([feat, edge_feat], dim=1)
            enhanced = edge_fusion(combined)

            enhanced_features.append(enhanced)

        if self.fusion_mode == 'bifpn':
            # BiFPN加权融合 - 需要统一尺寸
            # 以第一个特征图的尺寸为基准
            target_size = enhanced_features[0].shape[2:]

            # 调整所有特征图到相同尺寸
            resized_features = []
            for feat in enhanced_features:
                if feat.shape[2:] != target_size:
                    feat = F.interpolate(feat, size=target_size, mode='bilinear', align_corners=False)
                resized_features.append(feat)

            # 加权融合
            weights = self.relu(self.fusion_weights)
            weights = weights / (torch.sum(weights, dim=0) + self.epsilon)

            # 加权求和
            fused = sum(w * feat for w, feat in zip(weights, resized_features))
            return fused
        else:
            # 简单返回第一个特征图（测试用）
            return enhanced_features[0]


class Bottleneck_EMSCP_EIEM(Bottleneck):
    """增强的EMSCP Bottleneck with 边缘信息增强"""
    def __init__(self, c1, c2, shortcut=True, g=1, k=(3, 3), e=0.5):
        super().__init__(c1, c2, shortcut, g, k, e)
        c_ = int(c2 * e)  # hidden channels
        
        # 重新定义卷积层
        self.cv1 = Conv(c1, c_, k[0], 1)

        # EMSConvP需要至少64个通道，否则使用标准Conv
        if c2 >= 64:
            self.cv2 = EMSConvP(c2)  # 使用EMSCP模块
            if c_ != c2:
                self.channel_adjust = Conv(c_, c2, 1, 1)
            else:
                self.channel_adjust = None
        else:
            self.cv2 = Conv(c_, c2, k[1], 1, g=g)
            self.channel_adjust = None
        
        # 添加边缘信息增强
        self.eiem = EIEM(c2, c2)
        
    def forward(self, x):
        """Forward pass through the bottleneck with EIEM."""
        x1 = self.cv1(x)
        # 如果需要通道调整（仅对EMSConvP）
        if hasattr(self, 'channel_adjust') and self.channel_adjust is not None:
            x1 = self.channel_adjust(x1)
        y = self.cv2(x1)

        # 应用边缘信息增强
        y = self.eiem(y)

        # 残差连接
        return x + y if self.add else y


class C2f_EMSCP_EIEM(C2f):
    """C2f with EMSCP and Edge Information Enhancement"""
    def __init__(self, c1, c2, n=1, shortcut=False, g=1, e=0.5):
        super().__init__(c1, c2, n, shortcut, g, e)
        
        # 使用增强的Bottleneck
        self.m = nn.ModuleList(
            Bottleneck_EMSCP_EIEM(self.c, self.c, shortcut, g, k=(3, 3), e=1.0) 
            for _ in range(n)
        )
        
        # 在最后添加边缘信息增强
        self.global_eiem = EIEM(c2, c2)
        
    def forward(self, x):
        """Forward pass through C2f layer with EIEM."""
        # 标准C2f流程
        y = list(self.cv1(x).chunk(2, 1))
        y.extend(m(y[-1]) for m in self.m)
        output = self.cv2(torch.cat(y, 1))
        
        # 应用全局边缘信息增强
        output = self.global_eiem(output)
        
        return output


def test_modules():
    """测试模块功能"""
    print("🧪 测试创新点2模块...")
    
    # 测试参数
    batch_size = 2
    channels = 256
    height, width = 32, 32
    
    # 创建测试输入
    x = torch.randn(batch_size, channels, height, width)
    print(f"输入张量形状: {x.shape}")
    
    # 测试SobelConv
    print("\n1. 测试SobelConv...")
    sobel = SobelConv(channels)
    out_sobel = sobel(x)
    print(f"Sobel输出形状: {out_sobel.shape}")
    print(f"Sobel参数量: {sum(p.numel() for p in sobel.parameters()):,}")
    
    # 测试EIEM
    print("\n2. 测试EIEM...")
    eiem = EIEM(channels, channels)
    out_eiem = eiem(x)
    print(f"EIEM输出形状: {out_eiem.shape}")
    print(f"EIEM参数量: {sum(p.numel() for p in eiem.parameters()):,}")
    
    # 测试EdgeEnhancedFusion
    print("\n3. 测试EdgeEnhancedFusion...")
    features = [
        torch.randn(batch_size, 256, 32, 32),  # P3
        torch.randn(batch_size, 256, 16, 16),  # P4
        torch.randn(batch_size, 256, 8, 8),    # P5
    ]
    eef = EdgeEnhancedFusion([256, 256, 256])
    out_eef = eef(features)
    print(f"EEF输出形状: {out_eef.shape}")
    print(f"EEF参数量: {sum(p.numel() for p in eef.parameters()):,}")
    
    # 测试C2f_EMSCP_EIEM
    print("\n4. 测试C2f_EMSCP_EIEM...")
    c2f = C2f_EMSCP_EIEM(channels, channels, n=3)
    out_c2f = c2f(x)
    print(f"C2f输出形状: {out_c2f.shape}")
    print(f"C2f参数量: {sum(p.numel() for p in c2f.parameters()):,}")
    
    print("\n✅ 所有模块测试通过！")


if __name__ == "__main__":
    test_modules()
