# 论文画图相关视频整理

### 网络结构图

1. [什么？你说你不会画模型结构图？行吧，那你进来看看吧，手把手教你画YAML结构图！](https://www.bilibili.com/video/BV1X94y1K76Z/)
2. [什么？你说你更换主干后看不懂配置文件也不懂画结构图？那你快点进来看看了！](https://www.bilibili.com/video/BV1WA4m1V7nQ/)
3. [从简到难手把手教你画Pytorch模块内的结构图！](https://www.bilibili.com/video/BV1dC411p7H7/)

### 论文可视化图像和指标

1. 热力图可视化

    1. 需要安装grad-cam==1.5.4
    2. 使用示例：https://www.bilibili.com/video/BV1fU421o7jH/  如果更换了主干还需看：https://www.bilibili.com/video/BV1F6421V77v/
    3. 进度条不满是正常现象,只要进度条不是0,都可以进行出图.

2. 网络中间层的特征图可视化

    detect.py中设置visualize=True,其会保存每一层的特征图(前32张),相关代码在ultralytics/utils/plotting.py中的feature_visualization函数中,有需要可以自行修改.

3. 模型有效感受野可视化

    使用示例：https://www.bilibili.com/video/BV1Gx4y1v7ZZ/

4. 曲线对比图可视化

    可看百度云视频-plot_result.py使用教程。
    各种不同类型的模型曲线对比图和精度对比图也可以看这期视频：https://www.bilibili.com/video/BV1yf421X7t5/

5. 计算COCO指标和TIDE指标

    python dataset/yolo2coco.py --image_path dataset/images/test --label_path dataset/labels/test  
    python get_COCO_metrice.py --pred_json runs/val/exp/predictions.json --anno_json data.json  
    视频教程请看百度云视频-计算COCO指标教程.

6. 可视化并统计每张图的True Positive、False Positive、False Negative

    使用示例：https://www.bilibili.com/video/BV1RA4m1L79K/

7. PR曲线对比图绘制

    B站视频链接：https://www.bilibili.com/video/BV1uC41177oE/