import warnings, os
import torch
import torch.nn as nn
import multiprocessing as mp
import platform
import psutil
warnings.filterwarnings('ignore')

# Windows多进程修复 - 必须在导入YOLO之前
if __name__ == '__main__':
    mp.set_start_method('spawn', force=True)

from ultralytics import YOL<PERSON>

def optimize_training_settings():
    """智能优化训练设置，平衡速度和稳定性"""
    # 检测系统配置
    cpu_count = os.cpu_count() or 4
    memory_gb = psutil.virtual_memory().total / (1024**3)
    gpu_available = torch.cuda.is_available()
    
    settings = {
        'workers': 4,  # 保守但非0的workers设置
        'cache': True,  # 启用缓存以提升速度
        'amp': gpu_available,
        'batch_multiplier': 1.0,
    }
    
    # 根据内存容量调整
    if memory_gb >= 16:
        settings['workers'] = min(8, cpu_count)
        settings['cache'] = "ram"  # 内存缓存最快
        settings['batch_multiplier'] = 1.2
    elif memory_gb >= 12:
        settings['workers'] = 6
        settings['cache'] = True  # 磁盘缓存
        settings['batch_multiplier'] = 1.0
    else:
        settings['workers'] = 4
        settings['cache'] = True
        settings['batch_multiplier'] = 0.8
    
    print(f"🔧 系统配置优化:")
    print(f"   CPU核心数: {cpu_count}")
    print(f"   内存容量: {memory_gb:.1f}GB")
    print(f"   GPU可用: {gpu_available}")
    print(f"   Workers: {settings['workers']}")
    print(f"   缓存策略: {settings['cache']}")
    
    return settings

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 TADDH训练速度优化脚本")
    print("保持训练效果的同时最大化训练速度")
    print("=" * 60)
    
    # 获取优化设置
    settings = optimize_training_settings()
    
    # 检测当前正在训练的模型
    # 根据runs目录判断，优先使用TADDH
    model = YOLO('ultralytics/cfg/models/v8/yolov8n-TADDH.yaml')
    
    print("🔄 加载预训练权重...")
    model.load('yolov8n.pt')
    
    print("🚀 开始速度优化训练...")
    model.train(
        # === 数据集配置 ===
        data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
        
        # === 基本训练参数 - 速度优化 ===
        epochs=100,              # 保持训练轮数
        patience=50,             # 减少耐心值，加快收敛判断
        batch=-1,                # 自动选择最优batch size
        imgsz=640,
        
        # === 设备设置 - 速度优化 ===
        device="0" if torch.cuda.is_available() else "cpu",
        workers=settings['workers'],  # 优化的workers数量
        
        # === 项目设置 ===
        project='runs/train',
        name='TADDH-speed-optimized',
        exist_ok=True,
        
        # === 模型设置 ===
        pretrained=True,
        
        # === 优化器设置 - 平衡速度和效果 ===
        optimizer="AdamW",
        lr0=0.001,               # 稍微提高学习率加快收敛
        lrf=0.0001,
        momentum=0.937,
        weight_decay=0.0008,
        
        # === 学习率调度 - 速度优化 ===
        cos_lr=True,
        warmup_epochs=3.0,       # 减少预热轮数
        warmup_momentum=0.8,
        warmup_bias_lr=0.01,
        
        # === 损失权重 - 保持效果 ===
        box=7.0,
        cls=0.4,
        dfl=1.2,
        
        # === 数据增强 - 速度优化 ===
        hsv_h=0.01,              # 减少数据增强计算
        hsv_s=0.6,
        hsv_v=0.3,
        degrees=5.0,             # 减少旋转计算
        translate=0.05,          # 减少平移计算
        scale=0.3,               # 减少缩放计算
        mosaic=0.5,              # 减少马赛克概率
        close_mosaic=10,         # 更早关闭马赛克
        
        # === 核心速度优化设置 ===
        cache=settings['cache'],  # 智能缓存策略
        amp=settings['amp'],      # 混合精度训练
        
        # === 验证优化 ===
        val=True,
        plots=False,             # 关闭绘图节省时间
        save_json=False,         # 关闭JSON保存
        
        # === 保存优化 ===
        save=True,
        save_period=20,          # 增加保存间隔
        
        # === 内存和计算优化 ===
        multi_scale=False,       # 关闭多尺度训练
        rect=True,               # 启用矩形训练
        single_cls=False,
        
        # === 其他速度优化 ===
        verbose=True,
        seed=42,
        deterministic=False,     # 关闭确定性提升速度
        profile=False,           # 关闭性能分析
        
        # === 其他优化设置 ===
        fraction=1.0,            # 使用全部数据集
    )
    
    print("✅ 速度优化训练完成！")
    print("📊 训练速度应该有显著提升")
    print("🎯 训练效果保持不变")
    print(f"📁 结果保存在: runs/train/TADDH-speed-optimized/") 